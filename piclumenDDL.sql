<<<<<<< HEAD
ALTER TABLE `piclumen`.`stripe_invoice` ADD COLUMN `customer_country` varchar(255) NULL AFTER `currency`;
ALTER TABLE `piclumen`.`pay_lumen_record` ADD COLUMN `invalid_message` varchar(255) NULL AFTER `invalid`;
ALTER TABLE `piclumen`.`pay_logic_purchase_record` MODIFY COLUMN `cancel` tinyint(1)  DEFAULT 0 COMMENT '是否取消' AFTER `amount`;
UPDATE `piclumen`.`pay_logic_purchase_record` SET `cancel` = 0 WHERE  cancel is null;


-----------------------------------------------------------------------------------------------------------------------------------
ALTER TABLE `piclumen`.`subscription_current`
    ADD COLUMN `invalid` tinyint NULL DEFAULT 0 COMMENT '是否无效，存在cancel的情况' AFTER `auto_renew_status`;


-- 生成 0-19 共 20 个分表的 ALTER TABLE 语句
ALTER TABLE gpt_prompt_record_0
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_0
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_1
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_1
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_2
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_2
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_3
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_3
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_4
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_4
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_5
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_5
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_6
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_6
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_7
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_7
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_8
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_8
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_9
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_9
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_10
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_10
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_11
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_11
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_12
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_12
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_13
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_13
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_14
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_14
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_15
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_15
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_16
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_16
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_17
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_17
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_18
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_18
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;

ALTER TABLE gpt_prompt_record_19
    ADD main_category VARCHAR(50) NULL COMMENT '风格主要分类' AFTER fast_hour;
ALTER TABLE gpt_prompt_record_19
    ADD sub_category VARCHAR(50) NULL COMMENT '风格次要分类' AFTER main_category;



=====================================================2025-02-26 同步生产环境=====================================================================



create table gpt_sys_update
(
    id           bigint primary key,
    title        varchar(200)     null comment '标题信息',
    introduction varchar(500)     null comment '简介',
    details      longtext         null comment '详情',
    create_time  datetime         null comment '创建时间',
    update_time  datetime         null comment '修改时间',
    create_by    varchar(80)      null comment '创建者',
    update_by    varchar(80)      null comment '修改者',
    publish      bit default b'0' null comment '0 ： 未发布  1 ：已发布',
    publish_time datetime         null comment '发布时间',
    platform     varchar(255)     null comment '来源平台'
)
    comment '系统更新消息表';

create index create_timestamp_index
    on gpt_sys_update (create_time);


-- auto-generated definition
create table gpt_platform_activity
(
    id           bigint primary key,
    title        varchar(200)     null comment '标题信息',
    introduction varchar(500)     null comment '简介',
    details      longtext         null comment '详情',
    link_address varchar(100)     null comment '跳转地址',
    user_type    varchar(20)      null comment 'vip : 会员 not_vip : 非会员 all : 所有用户',
    create_time  datetime         null comment '创建时间',
    update_time  datetime         null comment '修改时间',
    create_by    varchar(80)      null comment '创建者',
    update_by    varchar(80)      null comment '修改者',
    publish      bit default b'0' null comment '0 ： 未发布  1 ：已发布',
    publish_time datetime         null comment '发布时间'
)
    comment '平台活动信息表';

create index create_timestamp_index
    on gpt_platform_activity (create_time);


----------------------------------------comments-----------------------------------

db.comments.createIndex(
    { _id: -1, deleted: 1 },
    { name: "id_deleted_desc_index" }
);


if (!db.getCollectionNames().includes("user_platform_activity")) {
    db.createCollection("user_platform_activity");
}
db.user_platform_activity.createIndex(
    { "accountInfo.userId": 1, "platformActivityId": 1 },
    { name: "userId_platformActivityId_index" }
);

if (!db.getCollectionNames().includes("user_sys_update")) {
    db.createCollection("user_sys_update");
}
db.user_sys_update.createIndex(
    { "accountInfo.userId": 1, "sysUpdateId": 1 },
    { name: "userId_sysUpdateId_index" }
);



=====================================================2025-03-06 同步生产环境=====================================================================


ALTER TABLE gpt_user_collect_classify_0 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_1 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_2 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_3 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_4 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_5 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_6 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_7 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_8 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_9 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_10 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_11 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_12 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_13 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_14 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_15 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_16 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_17 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_18 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;
ALTER TABLE gpt_user_collect_classify_19 ADD COLUMN cover VARCHAR(2000) NULL COMMENT '封面' AFTER size;


-- 删除旧索引
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_0;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_1;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_2;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_3;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_4;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_5;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_6;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_7;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_8;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_9;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_10;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_11;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_12;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_13;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_14;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_15;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_16;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_17;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_18;
DROP INDEX login_name_del_create_time_index ON gpt_prompt_file_19;

-- 创建新索引
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_0 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_1 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_2 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_3 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_4 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_5 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_6 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_7 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_8 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_9 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_10 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_11 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_12 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_13 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_14 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_15 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_16 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_17 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_18 (login_name, del, collect_nums,create_time);
CREATE INDEX loginName_del_collectNums_createTime_index
    ON gpt_prompt_file_19 (login_name, del, collect_nums,create_time);

-- 删除旧索引
DROP INDEX classify_id_index ON gpt_user_collect_0;
DROP INDEX classify_id_index ON gpt_user_collect_1;
DROP INDEX classify_id_index ON gpt_user_collect_2;
DROP INDEX classify_id_index ON gpt_user_collect_3;
DROP INDEX classify_id_index ON gpt_user_collect_4;
DROP INDEX classify_id_index ON gpt_user_collect_5;
DROP INDEX classify_id_index ON gpt_user_collect_6;
DROP INDEX classify_id_index ON gpt_user_collect_7;
DROP INDEX classify_id_index ON gpt_user_collect_8;
DROP INDEX classify_id_index ON gpt_user_collect_9;
DROP INDEX classify_id_index ON gpt_user_collect_10;
DROP INDEX classify_id_index ON gpt_user_collect_11;
DROP INDEX classify_id_index ON gpt_user_collect_12;
DROP INDEX classify_id_index ON gpt_user_collect_13;
DROP INDEX classify_id_index ON gpt_user_collect_14;
DROP INDEX classify_id_index ON gpt_user_collect_15;
DROP INDEX classify_id_index ON gpt_user_collect_16;
DROP INDEX classify_id_index ON gpt_user_collect_17;
DROP INDEX classify_id_index ON gpt_user_collect_18;
DROP INDEX classify_id_index ON gpt_user_collect_19;

-- 创建新索引
CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_0 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_1 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_2 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_3 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_4 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_5 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_6 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_7 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_8 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_9 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_10 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_11 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_12 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_13 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_14 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_15 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_16 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_17 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_18 (classify_id, login_name, create_time, id);

CREATE INDEX classify_id_login_name_create_time_id_index
    ON gpt_user_collect_19 (classify_id, login_name, create_time, id);


/*
  定时任务，清除用户的旧的统计数据(统计lumen币的情况)  unlink user_recharge_use_lumens

  redis 删除 提示词风格缓存 cache:PromptTemplates

  redis 删除 延时等待时间换成（修改为最新的）cache:delay_times
 */


alter table gpt_user
    add device_token varchar(200) null comment '设备token';



=====================================================2025-03-18 同步生产环境=====================================================================


alter table gpt_user drop column device_token;

alter table gpt_user
    add ios_device_token varchar(500) null comment 'ios 原生设备token';

alter table gpt_user
    add android_device_token varchar(500) null comment 'android 原生设备token';

alter table gpt_user
    add ios_fcm_token varchar(500) null comment 'ios设备token';

alter table gpt_user
    add android_fcm_token varchar(500) null comment 'android设备token';


=====================================================2025-03-21 同步生产环境=====================================================================

/*删除reids操作
unlink  "cache:large_resource_delay_times"

unlink  "cache:delay_times"
 */


=====================================================2025-03-25 同步生产环境=====================================================================

-- auto-generated definition
-- auto-generated definition
create table questionnaire_answer
(
    id                  bigint           not null
        primary key,
    login_name          varchar(80)      null,
    questionnaire_id    varchar(64)      null comment '问卷id',
    questionnaire_title varchar(100)     null comment '问卷标题',
    platform            varchar(255)     null comment '平台信息',
    sc_answer1          varchar(100)     null comment '单选题1答案',
    sc_answer2          varchar(100)     null comment '单选题2答案',
    sc_answer3          varchar(100)     null comment '单选题3答案',
    sc_answer4          varchar(100)     null comment '单选题4答案',
    sc_answer5          varchar(100)     null comment '单选题5答案',
    sc_answer6          varchar(100)     null comment '单选题6答案',
    sc_answer7          varchar(100)     null comment '单选题7答案',
    sc_answer8          varchar(100)     null comment '单选题8答案',
    sc_answer9          varchar(100)     null comment '单选题9答案',
    sc_answer10         varchar(100)     null comment '单选题10答案',
    mc_answer1          varchar(500)     null comment '多选题1答案',
    mc_answer2          varchar(500)     null comment '多选题2答案',
    mc_answer3          varchar(500)     null comment '多选题3答案',
    mc_answer4          varchar(500)     null comment '多选题4答案',
    mc_answer5          varchar(500)     null comment '多选题5答案',
    mc_answer6         varchar(500)      null comment '多选题6答案',
    mc_answer7         varchar(500)      null comment '多选题7答案',
    mc_answer8         varchar(500)      null comment '多选题8答案',
    mc_answer9         varchar(500)      null comment '多选题9答案',
    mc_answer10        varchar(500)      null comment '多选题10答案',
    essay_answer1       varchar(1000)     null comment '问答题1答案',
    essay_answer2       varchar(1000)     null comment '问答题2答案',
    essay_answer3       varchar(1000)     null comment '问答题3答案',
    essay_answer4       varchar(1000)     null comment '问答题4答案',
    essay_answer5       varchar(1000)     null comment '问答题5答案',
    grade_answer1       varchar(100)     null comment '评分题1答案',
    grade_answer2       varchar(100)     null comment '评分题2答案',
    grade_answer3       varchar(100)     null comment '评分题3答案',
    grade_answer4       varchar(100)     null comment '评分题4答案',
    grade_answer5       varchar(100)     null comment '评分题5答案',
    create_time         datetime         null comment '创建时间',
    update_time         datetime         null comment '修改时间',
    create_by           varchar(80)      null comment '创建者',
    update_by           varchar(80)      null comment '修改者',
    del                 bit default b'0' null comment '0:未删除 1：已删除'
)
    comment '用户问卷调查回答表';

create index loginName_questionnaire_id_index
    on questionnaire_answer (login_name, questionnaire_id);



create table questionnaire_record
(
    id                  bigint           not null primary key,
    title varchar(100)     null comment '问卷标题',
    introduction varchar(500)     null comment '简介',
    details    longtext     null comment '详情(问卷json数据)',
    platform            varchar(255)     null comment '平台信息',
    publish      TINYINT default b'0' null comment '0 ： 未发布  1 ：取消发布  5.已发布',
    start_time         datetime         null comment '开始时间',
    end_time         datetime         null comment '结束时间',
    create_time         datetime         null comment '创建时间',
    update_time         datetime         null comment '修改时间',
    create_by           varchar(80)      null comment '创建者',
    update_by           varchar(80)      null comment '修改者',
    del                 bit default b'0' null comment '0:未删除 1：已删除'
)
    comment '调查问卷记录表';


=====================================================2025-04-02 同步生产环境=====================================================================


create table gpt_img_control
(
    id           bigint primary key,
    user_id      bigint(20)       null COMMENT '用户ID',
    login_name   varchar(80)      null COMMENT '用户账号',
    img_url      varchar(1000)    null COMMENT '图片地址',
    img_type     varchar(30)      null COMMENT 'public : 公共的  private : 私有的',
    control_type varchar(30)      null COMMENT 'canny : 主体 depth : 轮廓 openpose : 姿势',
    create_time  datetime         null comment '创建时间',
    update_time  datetime         null comment '修改时间',
    create_by    varchar(80)      null comment '创建者',
    update_by    varchar(80)      null comment '修改者',
    del          bit(1) DEFAULT b'0' COMMENT   '0:未删除 1:已删除'
)
    comment '图片控制提取表';


create index login_name_img_type_control_type_index
    on gpt_img_control (login_name, img_type, control_type, create_time);


INSERT INTO piclumen.gpt_img_control (id, user_id, login_name, img_url, img_type, control_type, create_time, update_time, create_by, update_by, del) VALUES (1910982439071111705, 0, 'system', 'https://images.piclumen.com/piclumen/forever/pose_1.webp', 'public', 'openpose', null, null, null, null, false);
INSERT INTO piclumen.gpt_img_control (id, user_id, login_name, img_url, img_type, control_type, create_time, update_time, create_by, update_by, del) VALUES (1910982439072222705, 0, 'system', 'https://images.piclumen.com/piclumen/forever/pose_2.webp', 'public', 'openpose', null, null, null, null, false);
INSERT INTO piclumen.gpt_img_control (id, user_id, login_name, img_url, img_type, control_type, create_time, update_time, create_by, update_by, del) VALUES (1910982439073333705, 0, 'system', 'https://images.piclumen.com/piclumen/forever/pose_3.webp', 'public', 'openpose', null, null, null, null, false);
INSERT INTO piclumen.gpt_img_control (id, user_id, login_name, img_url, img_type, control_type, create_time, update_time, create_by, update_by, del) VALUES (1910982439074444705, 0, 'system', 'https://images.piclumen.com/piclumen/forever/pose_4.webp', 'public', 'openpose', null, null, null, null, false);
INSERT INTO piclumen.gpt_img_control (id, user_id, login_name, img_url, img_type, control_type, create_time, update_time, create_by, update_by, del) VALUES (1910982439075555705, 0, 'system', 'https://images.piclumen.com/piclumen/forever/pose_5.webp', 'public', 'openpose', null, null, null, null, false);

/**
  删除默认缓存
 */
unlink modelListKey

=====================================================2025-04-17 同步生产环境=====================================================================
ALTER TABLE `piclumen`.`gpt_img_control`
    ADD COLUMN `width` int NULL DEFAULT NULL COMMENT '图片宽度' AFTER `control_type`,
ADD COLUMN `height` int NULL DEFAULT NULL COMMENT '图片高度' AFTER `width`,
ADD COLUMN `real_width` int NULL DEFAULT NULL COMMENT '真实宽' AFTER `height`,
ADD COLUMN `real_height` int NULL DEFAULT NULL COMMENT '真实高' AFTER `real_width`;


=====================================================2025-04-30 同步生产环境=====================================================================
CREATE TABLE `banner_img` (
                              `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                              `img_url` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片链接地址',
                              `sort` int(10) NOT NULL DEFAULT 9999 COMMENT '图片顺序',
                              `status` tinyint(1) DEFAULT NULL COMMENT '图片状态：0-未发布，1-已发布',
                              `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
                              `create_by` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
                              `update_by` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='社区首页banner图配置表';


=====================================================2025-04-18 同步生产环境=====================================================================

db.files.createIndex({ "fileId": 1}, { name: "fileId" });

alter table gpt_public_file_review
    add activity_id bigint null comment '活动id' after brief;

create index file_id_index
    on gpt_public_file_review (file_id);

=====================================================2025-05-14 同步生产环境=====================================================================

db.files.createIndex({ "activityId": 1}, { name: "activityId" });

if (!db.getCollectionNames().includes("user_activity_record")) {
    db.createCollection("user_activity_record");
}
db.user_activity_record.createIndex({ "userId": 1}, { name: "userId" });

CREATE TABLE `push_message` (
                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                `push_type` int(10) DEFAULT NULL COMMENT '推送类型',
                                `push_time` datetime DEFAULT NULL COMMENT '推送时间',
                                `push_scope` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '推送范围(对应国家)',
                                `priority` int(10) DEFAULT NULL COMMENT '优先级',
                                `title` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标题',
                                `scheme_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'schemeUrl',
                                `pic_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '大图地址',
                                `thumbnail_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '缩略图地址',
                                `status` int(10) DEFAULT NULL COMMENT '推送状态：0-未推送，1-已推送，2-已过期，3-已暂停，-1-DEFAULT，-2-FAILED，5-正在推送',
                                `daily_auto_push` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否每日自动推送：0-否，1-是',
                                `create_by` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
                                `update_by` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='推送消息表';

CREATE TABLE `push_message_content` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                        `push_message_id` bigint DEFAULT NULL COMMENT '推送消息表ID',
                                        `language_code` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '语言编码，默认英语',
                                        `language_name` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '语言国家，默认英国',
                                        `title` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '标题',
                                        `body` text DEFAULT NULL COMMENT '消息内容',
                                        `create_by` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
                                        `update_by` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
                                        `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_messge_id` (`push_message_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='推送消息内容表';

CREATE TABLE `push_operation_record` (
                                         `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                         `push_message_id` bigint DEFAULT NULL COMMENT '推送消息表ID',
                                         `open_rate` decimal(5,2) DEFAULT NULL COMMENT 'PUSH打开占比',
                                         `uv_rate` decimal(5,2) DEFAULT NULL COMMENT 'PUSH引入UV占比',
                                         `open_sum` bigint DEFAULT NULL COMMENT '通过PUSH消息打开APP的次数',
                                         `uv` bigint DEFAULT NULL COMMENT '当日UV',
                                         `push_count` bigint DEFAULT NULL COMMENT '发送数量',
                                         `create_by` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
                                         `update_by` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
                                         `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_messge_id` (`push_message_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='推送操作记录表';

-- 插入每日自动推送消息内容
INSERT INTO push_message
(push_type, priority, title, status, daily_auto_push, create_by, update_by, create_time, update_time)
VALUES(1, 1, 'Free Lumen Awaits!', 0, 1, 'admin', 'admin', now(), now());

INSERT INTO push_message_content
(push_message_id, language_code, language_name, title, body, create_by, update_by, create_time, update_time)
VALUES
    (1, 'en', '英语', 'Free Lumen Awaits!', 'Claim your daily Lumen now!\nYour daily Lumen has arrived—don’t miss out! Tap to claim it now.', 'admin', 'admin', now(), now()),
    (1, 'de_DE', '德语', 'Kostenloses Lumen wartet!', 'Sichere dir jetzt dein tägliches Lumen!\nDein tägliches Lumen ist da – verpasse es nicht! Tippe jetzt, um es zu erhalten.', 'admin', 'admin', now(), now()),
    (1, 'ja_JP', '日语', '無料のLumenをゲットしよう！', '毎日もらえる無料Lumenを今すぐゲット！\n今日のLumenが届いています。ぜひタップして受け取ってくださいね！', 'admin', 'admin', now(), now()),
    (1, 'es', '西班牙语', '¡Lumen gratis te espera!', '¡Reclama tu Lumen diario ahora!\nTu Lumen diario ha llegado — ¡no lo dejes pasar! Toca para obtenerlo.', 'admin', 'admin', now(), now()),
    (1, 'pt_BR', '葡萄牙语', 'Lumen grátis te espera!', 'Resgate seu Lumen diário agora!\nSeu Lumen diário chegou — não perca! Toque para resgatar agora.', 'admin', 'admin', now(), now()),
    (1, 'fr_FR', '法语', 'Un Lumen gratuit vous attend !', 'Récupérez votre Lumen quotidien dès maintenant !\nVotre Lumen du jour est arrivé — ne le manquez pas ! Appuyez pour le réclamer.', 'admin', 'admin', now(), now()),
    (1, 'ko', '韩语', '무료 Lumen이 도착했어요!', '오늘의 Lumen, 지금 받으세요!\n오늘의 Lumen이 도착했어요 — 놓치지 마세요! 지금 터치해서 받아보세요.', 'admin', 'admin', now(), now()),
    (1, 'it', '意大利语', 'Lumen gratuito ti aspetta!', 'Richiedi subito il tuo Lumen quotidiano!\nIl tuo Lumen giornaliero è arrivato — non perderlo! Tocca per reclamarlo ora.', 'admin', 'admin', now(), now()),
    (1, 'zh-TW', '繁体中文', '免費的 Lumen 等你領取！', '現在就來領取你的每日 Lumen！\n你的每日 Lumen 已到，不要錯過！點擊立即領取。', 'admin', 'admin', now(), now());

-- 国家配置表增加优先级字段
alter table gpt_specified_country add `priority` int(10) DEFAULT null COMMENT '优先级';

-- kpi_mix表增加三端UV统计字段
alter table kpi_mix add web_duv bigint default null comment 'web当日UV', add ios_duv bigint default null comment 'ios当日UV', add android_duv bigint default null comment 'android当日UV';
=====================================================2025-05-29 同步生产环境=====================================================================
ALTER TABLE `piclumen`.`stripe_product`
    ADD COLUMN `trial_day` int NULL COMMENT '试用天数' AFTER `lumen`,
ADD COLUMN `initial_lumen` int NULL COMMENT '试用赠送lumen数/首充' AFTER `trial_day`;

ALTER TABLE `piclumen`.`stripe_product`
    ADD COLUMN `status` tinyint NULL DEFAULT 0 COMMENT '状态' AFTER `vip_level`;

INSERT INTO `piclumen`.`stripe_product` (`id`, `stripe_product_id`, `stripe_price_id`, `plan_level`, `lumen`, `trial_day`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `status`, `mark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (6, 'prod_RaGUXrvWcw3euo', 'price_1RTZkSJkomjQvWKvrRPSefgq', NULL, 100, 0, 50, 'one', NULL, -1, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `piclumen`.`stripe_product` (`id`, `stripe_product_id`, `stripe_price_id`, `plan_level`, `lumen`, `trial_day`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `status`, `mark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (7, 'prod_RaGUXrvWcw3euo', 'price_1RTZkiJkomjQvWKvPoaty7GT', NULL, 1000, 0, 500, 'one', NULL, -1, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `piclumen`.`stripe_product` (`id`, `stripe_product_id`, `stripe_price_id`, `plan_level`, `lumen`, `trial_day`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `status`, `mark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (8, 'prod_RaGUXrvWcw3euo', 'price_1RTZkyJkomjQvWKv9w6IagwF', NULL, 10000, 0, 5000, 'one', NULL, -1, 1, NULL, NULL, NULL, NULL, NULL);

INSERT INTO `piclumen`.`stripe_product` (`id`, `stripe_product_id`, `stripe_price_id`, `plan_level`, `lumen`, `trial_day`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `status`, `mark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (9, 'prod_RaGEOn3G3XhxNY', 'price_1RTZcYJkomjQvWKvgJd8yyQ8', 'standard', 2000, 3, 200, 'plan', 'month', 1, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `piclumen`.`stripe_product` (`id`, `stripe_product_id`, `stripe_price_id`, `plan_level`, `lumen`, `trial_day`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `status`, `mark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (10, 'prod_RaGEOn3G3XhxNY', 'price_1RTZdYJkomjQvWKvMGeSRFYv', 'standard', 2000, 3, 200, 'plan', 'year', 2, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `piclumen`.`stripe_product` (`id`, `stripe_product_id`, `stripe_price_id`, `plan_level`, `lumen`, `trial_day`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `status`, `mark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (11, 'prod_RaGI1ZnY0UzdES', 'price_1RTZg9JkomjQvWKvGgVkaD9P', 'pro', 5000, 3, 500, 'plan', 'month', 3, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `piclumen`.`stripe_product` (`id`, `stripe_product_id`, `stripe_price_id`, `plan_level`, `lumen`, `trial_day`, `initial_lumen`, `product_type`, `price_interval`, `vip_level`, `status`, `mark`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (12, 'prod_RaGI1ZnY0UzdES', 'price_1RTZgfJkomjQvWKvxrVFupTc', 'pro', 5000, 3, 500, 'plan', 'year', 4, 1, NULL, NULL, NULL, NULL, NULL);

CREATE TABLE `stripe_trial_log` (
    `id` bigint NOT NULL COMMENT '主键',
    `user_id` bigint DEFAULT NULL,
    `login_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
    `subscription_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订阅ID',
    `price_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '价格ID',
    `trial_day` int DEFAULT NULL COMMENT '源计划ID',
    `trial_start` bigint DEFAULT NULL COMMENT '试用开始',
    `trial_end` bigint DEFAULT NULL COMMENT '使用结束',
    `trial_lumen` bigint DEFAULT NULL COMMENT '新计划ID',
    `create_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '更新者',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Paypal trial 日志表';


ALTER TABLE `piclumen`.`stripe_subscription_log` ADD COLUMN `trial_start` bigint NULL COMMENT '试用开始' AFTER `logic_Period_Start`;
ALTER TABLE `piclumen`.`stripe_subscription_log` ADD COLUMN `trial_end` bigint NULL COMMENT '结束试用' AFTER `trial_start`;
ALTER TABLE `piclumen`.`stripe_subscription_record` ADD COLUMN `trial_start` bigint NULL COMMENT '试用开始' AFTER `logic_Period_Start`;
ALTER TABLE `piclumen`.`stripe_subscription_record` ADD COLUMN `trial_end` bigint NULL COMMENT '结束试用' AFTER `trial_start`;
ALTER TABLE `piclumen`.`pay_logic_purchase_record` ADD COLUMN `trial` tinyint default 0 COMMENT '试用' AFTER `logic_period_end`;

ALTER TABLE `piclumen`.`subscription_current` ADD COLUMN `trial` tinyint default 0 COMMENT '状态' AFTER `invalid`;
ALTER TABLE `piclumen`.`subscription_current` ADD COLUMN `mark` VARCHAR(255) default 'v1'  COMMENT '版本' AFTER `invalid`;

ALTER TABLE `piclumen`.`pay_logic_purchase_record`
    MODIFY COLUMN `price_id` varchar(255) default NULL COMMENT 'stripe price_id' AFTER `subscription_id`



-- todo
-- com/lx/pl/pay/common/service/impl/PayLumenRecordServiceImpl.java:334 id 取线上表的最大的ID hasPurchasedLumen

--  user_vip_standards 需要清除Redis缓存

ALTER TABLE gpt_vip_standards ADD COLUMN lumen_discount int DEFAULT NULL COMMENT 'lumen折扣';

UPDATE `piclumen`.`gpt_vip_standards` SET `vip_type` = 'basic', `daily_lumens` = 10, `monthly_lumens` = 0, `lumen_discount` = 0, `creation_history` = '30', `task_queue` = 1, `concurrent_jobs` = 1, `batch_download` = b'0', `collect_num` = 500, `images_per_batch` = 2, `upscale` = b'1', `inpaint` = b'1', `expand` = b'1', `colorize` = b'1', `remove_bg` = b'1', `history_explore` = b'1', `translation` = b'1', `enhance` = b'1', `create_by` = '', `create_time` = '2024-12-25 13:28:28', `update_by` = '', `update_time` = NULL WHERE `id` = 1;
UPDATE `piclumen`.`gpt_vip_standards` SET `vip_type` = 'standard', `daily_lumens` = 10, `monthly_lumens` = 2000, `lumen_discount` = 10, `creation_history` = '-1', `task_queue` = 5, `concurrent_jobs` = 2, `batch_download` = b'1', `collect_num` = 5000, `images_per_batch` = 4, `upscale` = b'1', `inpaint` = b'1', `expand` = b'1', `colorize` = b'1', `remove_bg` = b'1', `history_explore` = b'1', `translation` = b'1', `enhance` = b'1', `create_by` = '', `create_time` = '2024-12-25 13:28:30', `update_by` = '', `update_time` = NULL WHERE `id` = 2;
UPDATE `piclumen`.`gpt_vip_standards` SET `vip_type` = 'pro', `daily_lumens` = 10, `monthly_lumens` = 5000, `lumen_discount` = 20, `creation_history` = '-1', `task_queue` = 10, `concurrent_jobs` = 5, `batch_download` = b'1', `collect_num` = 50000, `images_per_batch` = 4, `upscale` = b'1', `inpaint` = b'1', `expand` = b'1', `colorize` = b'1', `remove_bg` = b'1', `history_explore` = b'1', `translation` = b'1', `enhance` = b'1', `create_by` = '', `create_time` = '2024-12-25 13:28:31', `update_by` = '', `update_time` = NULL WHERE `id` = 3;

CREATE TABLE `stripe_coupon` (
     `id` bigint NOT NULL COMMENT '数据库主键 ID',
     `product_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'plan/one',
     `stripe_coupon_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Stripe 平台分配的优惠券唯一标识',
     `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '优惠券名称（可选）',
     `duration` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '优惠持续时间类型：forever / once / repeating',
     `duration_in_months` int DEFAULT NULL COMMENT '当 duration = repeating 时有效，表示优惠几个月',
     `percent_off` int DEFAULT NULL COMMENT '例如：20 表示八折优惠，优惠 20%',
     `max_redemptions` int DEFAULT NULL COMMENT '该优惠券的最大兑换总次数',
     `times_redeemed` int DEFAULT NULL,
     `redeem_by` bigint DEFAULT NULL COMMENT '截止兑换时间（UTC 时间）',
     `type` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
     `valid` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否有效：true / false',
     `mark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息（可选）',
     `celebrity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '红人名称',
     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
     `create_by` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '创建者',
     `update_by` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '修改者',
     PRIMARY KEY (`id`),
     UNIQUE KEY `uk_stripe_coupon_id` (`stripe_coupon_id`),
     UNIQUE KEY `u_off_idx` (`percent_off`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Stripe 优惠券表';

INSERT INTO `piclumen`.`stripe_coupon` (`id`, `product_type`, `stripe_coupon_id`, `name`, `duration`, `duration_in_months`, `percent_off`, `max_redemptions`, `times_redeemed`, `redeem_by`, `type`, `valid`, `mark`, `celebrity_name`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (10, 'all', 'Cw2e8PEy', '10% OFF', 'forever', NULL, 10, -1, NULL, NULL, NULL, 1, '10% OFF', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `piclumen`.`stripe_coupon` (`id`, `product_type`, `stripe_coupon_id`, `name`, `duration`, `duration_in_months`, `percent_off`, `max_redemptions`, `times_redeemed`, `redeem_by`, `type`, `valid`, `mark`, `celebrity_name`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (20, 'all', 'JsqTE7gF', '20% OFF', 'forever', NULL, 20, -1, NULL, NULL, NULL, 1, '20% OFF', NULL, NULL, NULL, NULL, NULL);


=====================================================2025-05-30 同步生产环境=====================================================================
-- 用户表增加系统奖励点数字段
alter table gpt_user add system_reward_lumen int default null comment '系统奖励点数';

-- 修改PUSH消息主表推送国家范围长度
alter table push_message modify push_scope varchar(3000) default null comment '推送范围(对应国家)';

alter table comm_activity_reward_selection
    add mini_thumbnail_url varchar(255) null comment 'mini 缩略图' after comment_num;

alter table gpt_public_file_review
    add model_display varchar(30) null comment '模型名称' after activity_id;
=====================================================2025-06-19 同步生产环境=====================================================================
-- 删除无用记录表
drop table if exists gpt_action_log;

=====================================================2025-06-26 同步生产环境=====================================================================
db.files.createIndex(
  { "accountInfo.userId": 1, activityId: 1 },
  { name: "accountUserId_activityId_idx" }
);


=========================================================优惠券=======================
ALTER TABLE stripe_product ADD COLUMN price varchar(255) DEFAULT NULL COMMENT '价格' after  price_interval;

UPDATE `piclumen`.`stripe_product` SET `price` = '0.99' WHERE lumen = 100;
UPDATE `piclumen`.`stripe_product` SET `price` = '8.99' WHERE lumen = 1000;
UPDATE `piclumen`.`stripe_product` SET `price` = '79.99' WHERE lumen = 10000;

UPDATE `piclumen`.`stripe_product` SET `price` = '11.99' WHERE plan_level = 'standard' and price_interval = "month" and `status` = 1;
UPDATE `piclumen`.`stripe_product` SET `price` = '107.88' WHERE plan_level = 'standard' and price_interval = "year" and `status` = 1 ;
UPDATE `piclumen`.`stripe_product` SET `price` = '28.99' WHERE plan_level = 'pro' and price_interval = "month" and `status` = 1;
UPDATE `piclumen`.`stripe_product` SET `price` = '263.88' WHERE plan_level = 'pro' and price_interval = "year" and `status` = 1;


