package com.lx.pl.pay.stripe.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.db.mysql.gen.entity.StripeUserCustomer;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.StripeErrorCode;
import com.lx.pl.exception.BadRequestException;
import com.lx.pl.exception.PaymentException;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.common.dto.CouponVo;
import com.lx.pl.pay.common.enums.PromotionType;
import com.lx.pl.pay.common.service.PayCouponService;
import com.lx.pl.pay.common.service.PromotionConfigService;
import com.lx.pl.pay.common.service.StripeCouponService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.paypal.model.vo.ProductItem;
import com.lx.pl.pay.stripe.domain.StripeInvoice;
import com.lx.pl.pay.stripe.domain.StripePaymentIntent;
import com.lx.pl.pay.stripe.dto.BuyItemDto;
import com.lx.pl.pay.stripe.dto.CheckoutSessionRequest;
import com.lx.pl.pay.stripe.dto.PaymentType;
import com.lx.pl.pay.stripe.dto.SubscriptionScheduleDTO;
import com.lx.pl.pay.stripe.service.StripeInvoiceService;
import com.lx.pl.pay.stripe.service.StripePayService;
import com.lx.pl.pay.stripe.service.StripePaymentIntentService;
import com.lx.pl.service.StripeCustomerService;
import com.lx.pl.service.StripeProductService;
import com.lx.pl.service.VipService;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.model.checkout.Session;
import com.stripe.model.checkout.SessionCollection;
import com.stripe.param.*;
import com.stripe.param.checkout.SessionCreateParams;
import com.stripe.param.checkout.SessionListParams;
import com.stripe.param.common.EmptyParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.lx.pl.pay.common.util.PayConstant.*;

@Service
public class StripePayServiceImpl implements StripePayService {

    static Logger log = LoggerFactory.getLogger("stripe-pay-msg");

    @Value("${stripe.client.secretKey}")
    private String secretKey;
    @Autowired
    private StripeCustomerService stripeCustomerService;
    @Autowired
    private StripeProductService stripeProductService;
    @Autowired
    private SubscriptionCurrentService subscriptionCurrentService;
    @Autowired
    private StripeInvoiceService stripeInvoiceService;
    @Autowired
    private StripePaymentIntentService stripePaymentIntentService;
    @Autowired
    private VipService vipService;
    @Autowired
    private StripeCouponService stripeCouponService;
    @Autowired
    private PayCouponService payCouponService;
    @Autowired
    private PromotionConfigService promotionConfigService;


    @Override
    public Session createPayment(BuyItemDto buyItemDto, User user) {
        // 设置 Stripe API 密钥
        Stripe.apiKey = secretKey;
        //目前只支持同时购买一种

        com.lx.pl.pay.paypal.model.vo.ProductItem stripeItem = buyItemDto.getStripeItems().get(0);
        // 获取或创建 StripeCustomer
        StripeUserCustomer stripeUserCustomer = getOrCreateStripeCustomer(user);
        // 获取支付类型
        PaymentType paymentType = stripeItem.getType();


        // 根据code查询有效的优惠券
//        if (!payCouponService.checkCouponCodeCanUse(buyItemDto.getCouponCode(), user, paymentType)) {
//            throw new PaymentException(StripeErrorCode.COUPON_MAX_REDEMPTIONS);
//        }
        if (buyItemDto.getCouponCode() != null) {
            CouponVo coupon = payCouponService.queryStripeCouponCodeByCouponCodeCheck(buyItemDto.getCouponCode(), buyItemDto.getStripeItems());
            buyItemDto.setCouponVo(coupon);
        }
        switch (paymentType) {
            case PLAN:
                return handlePlanPayment(buyItemDto, stripeItem, stripeUserCustomer);
            case ONE:
                return handleOneTimePayment(buyItemDto, stripeUserCustomer);
            default:
                log.error("Unsupported payment type: {}", paymentType);
                throw new PaymentException(StripeErrorCode.UNSUPPORTED_PAYMENT_TYPE);
        }
    }

    @Override
    public void upgradeSubscription(BuyItemDto buyItemDto, User user) {
        log.info("业务调用升级订阅开始，补差价方式, buyItemDto:{}, user:{}", buyItemDto, user);
        Stripe.apiKey = secretKey;
        //目前只支持同时购买一种
        ProductItem stripeItem = buyItemDto.getStripeItems().get(0);
        StripeUserCustomer stripeUserCustomer = getStripeCustomer(user);
        // 判断是否跨平台支付
        crossPlatformPaymentCheck(stripeUserCustomer);

        //判断stripeItem中的type必须为plan，否者报错
        if (!PaymentType.PLAN.equals(stripeItem.getType())) {
            log.error("Invalid payment type: {}", stripeItem.getType());
            throw new PaymentException(StripeErrorCode.UNSUPPORTED_PAYMENT_TYPE);
        }
        String customerId = stripeUserCustomer.getCustomerId();

        // Step 1: 查询客户的激活订阅
        Subscription oldSubscription = findActiveSubscription(customerId);
        if (oldSubscription == null) {
            log.error("No active subscription found for customer: {}", customerId);
            throw new PaymentException(StripeErrorCode.ACTIVE_SUBSCRIPTION_REQUIRED);
        }

        // Step 2: 校验能否升级订阅
        StripeProduct newStripeProduct = stripeProductService.getStripeProductByBuyItemDto(stripeItem);

        // 获取订阅项 只支持1个订阅项
        SubscriptionItem subscriptionItem = oldSubscription.getItems().getData().get(0);
        StripeProduct oldStripeProduct = stripeProductService.getStripeProductByPriceId(subscriptionItem.getPrice().getId());
        if (oldStripeProduct == null) {
            log.error("No stripe product found for priceId: {}, user:{}, customerId:{}", subscriptionItem.getPrice()
                    .getId(), user.getLoginName(), customerId);
            throw new PaymentException(StripeErrorCode.NO_STRIPE_PRODUCT_FOUND);
        }
        boolean canUpgrade = canUpgradeSubscription(oldStripeProduct, newStripeProduct);
        if (!canUpgrade) {
            throw new PaymentException(StripeErrorCode.CANNOT_UPGRADE_SUBSCRIPTION);
        }

        String newPriceId = newStripeProduct.getStripePriceId();

        // Step 3: 检查是否已有active状态的 SubscriptionSchedule，
        List<SubscriptionSchedule> existingSchedules = findAllRelevantSubscriptionSchedules(customerId);
        for (SubscriptionSchedule schedule : existingSchedules) {
            if ("active".equals(schedule.getStatus())) {
                // 处理 active 状态的订阅计划（当前请求不能执行）
                log.error("发现 active 订阅计划: scheduleId:{} for customer: {}, user:{}", schedule.getId(), customerId, user.getLoginName());
                throw new PaymentException(StripeErrorCode.HAVE_ACTIVE_SUBSCRIPTION_SCHEDULE);
            }
        }

        // Step 4: 检查是否已有not_started状态 SubscriptionSchedule，并取消它们
        for (SubscriptionSchedule schedule : existingSchedules) {
            if ("not_started".equals(schedule.getStatus())) {
                // 取消 not_started 状态的订阅计划
                cancelSubscriptionSchedule(schedule);
                log.info("取消 not_started 订阅计划: {}", schedule.getId());
            }
        }

        try {
            updateSubscriptionForItemPrice(oldSubscription, subscriptionItem, newPriceId);
        } catch (StripeException e) {
            throw new PaymentException(StripeErrorCode.UPDATE_SUBSCRIPTION_ITEM_PRICE_ERROR, e);
        }
        log.info("业务调用升级订阅结束，补差价方式：oldSubscriptionId:{}, customerId:{}, newPriceId:{}. ", oldSubscription.getId(), customerId, newPriceId);

    }

    @Override
    public void upgradeSubscriptionByNewProduct(BuyItemDto buyItemDto, User user) {
        log.info("业务调用取消旧订阅并创建新订阅，可能为升级开始, buyItemDto:{}, user:{}", buyItemDto, user);
        Stripe.apiKey = secretKey;
        //目前只支持同时购买一种
        com.lx.pl.pay.paypal.model.vo.ProductItem stripeItem = buyItemDto.getStripeItems().get(0);
        StripeUserCustomer stripeUserCustomer = getStripeCustomer(user);
        // 判断是否跨平台支付
        crossPlatformPaymentCheck(stripeUserCustomer);

        //判断stripeItem中的type必须为plan，否者报错
        if (!PaymentType.PLAN.equals(stripeItem.getType())) {
            log.error("Invalid payment type: {}", stripeItem.getType());
            throw new PaymentException(StripeErrorCode.UNSUPPORTED_PAYMENT_TYPE);
        }
        String customerId = stripeUserCustomer.getCustomerId();

        // Step 1: 查询客户的激活订阅
        Subscription oldSubscription = findActiveSubscription(customerId);
        if (oldSubscription == null) {
            log.error("No active subscription found for customer: {}", customerId);
            throw new PaymentException(StripeErrorCode.ACTIVE_SUBSCRIPTION_REQUIRED);
        }

        // Step 2: 校验能否升级订阅
        StripeProduct newStripeProduct = stripeProductService.getStripeProductByBuyItemDto(stripeItem);

        // 获取订阅项 只支持1个订阅项
        SubscriptionItem subscriptionItem = oldSubscription.getItems().getData().get(0);
        StripeProduct oldStripeProduct = stripeProductService.getStripeProductByPriceId(subscriptionItem.getPrice().getId());
        if (oldStripeProduct == null) {
            log.error("No stripe product found for priceId: {}, user:{}, customerId:{}", subscriptionItem.getPrice().getId(), user.getLoginName(), customerId);
            throw new PaymentException(StripeErrorCode.NO_STRIPE_PRODUCT_FOUND);
        }
        Pair<Boolean, Boolean> booleanBooleanPair = canUpgradeSubscriptionByDefined(oldStripeProduct, newStripeProduct);
        boolean canUpgrade = booleanBooleanPair.getLeft();
        boolean isRealUpgrade = booleanBooleanPair.getRight();
        if (!canUpgrade) {
            throw new PaymentException(StripeErrorCode.CANNOT_UPGRADE_SUBSCRIPTION);
        }

        String newPriceId = newStripeProduct.getStripePriceId();

        // Step 3: 检查是否已有active状态的 SubscriptionSchedule，
        List<SubscriptionSchedule> existingSchedules = findAllRelevantSubscriptionSchedules(customerId);
        for (SubscriptionSchedule schedule : existingSchedules) {
            if ("active".equals(schedule.getStatus())) {
                // 处理 active 状态的订阅计划（当前请求不能执行）
                log.error("发现 active 订阅计划: scheduleId:{} for customer: {}, user:{}", schedule.getId(), customerId, user.getLoginName());
                throw new PaymentException(StripeErrorCode.HAVE_ACTIVE_SUBSCRIPTION_SCHEDULE);
            }
        }

        // Step 4: 检查是否已有not_started状态 SubscriptionSchedule，并取消它们
        for (SubscriptionSchedule schedule : existingSchedules) {
            if ("not_started".equals(schedule.getStatus())) {
                // 取消 not_started 状态的订阅计划
                cancelSubscriptionSchedule(schedule);
                log.info("取消 not_started 订阅计划: {}", schedule.getId());
            }
        }

        if (isRealUpgrade) {
            try {
                updateSubscriptionItemPrice(subscriptionItem, newPriceId);
            } catch (StripeException e) {
                throw new PaymentException(StripeErrorCode.UPDATE_SUBSCRIPTION_ITEM_PRICE_ERROR, e);
            }
        } else {
            log.info("业务调用取消旧订阅并创建新订阅开始, buyItemDto:{}, user:{}", buyItemDto, user);
            // Step 5: 如果取消旧订阅成功，开始新订阅
            try {
                cancelSubscriptionNow(oldSubscription);
            } catch (StripeException e) {
                // 如果旧订阅取消失败，中止流程
                log.error("Failed to cancel old subscription: user:{},customerId:{}", user.getLoginName(), customerId, e);
                throw new PaymentException(StripeErrorCode.CANCEL_SUBSCRIPTION_ERROR, e);
            }
            log.info("业务调用取消旧订阅并创建新订阅中间过程，cancel old subscription success: user:{}, customerId:{}", user.getLoginName(), customerId);
            // Step 6: 尝试创建新订阅
            Subscription newSubscription = null;
            try {
                newSubscription = createNewSubscription(customerId, newPriceId);
            } catch (StripeException e) {
                // 新订阅创建失败，不进行取消操作
                log.error("Failed to create new subscription: user:{},customerId:{}, newPriceId:{}", user.getLoginName(), customerId, newPriceId, e);
                throw new PaymentException(StripeErrorCode.CREATE_NEW_SUBSCRIPTION_FAILED, e);
            }
            log.info("业务调用取消旧订阅并创建新订阅，可能为升级结束：oldSubscriptionId:{}, customerId:{}, newPriceId:{}. New subscription ID: {}", oldSubscription.getId(), customerId, newPriceId, newSubscription.getId());

        }

    }

    @Override
    public void cancelSubscriptionFuture(User user) {
        log.info("业务调用未来取消订阅（取消自动续费）开始, user:{}", user);
        Stripe.apiKey = secretKey;

        StripeUserCustomer stripeUserCustomer = getStripeCustomer(user);
        String customerId = stripeUserCustomer.getCustomerId();

        // Step 1: 查询客户的激活订阅
        Subscription subscription = findActiveSubscription(customerId);
        if (subscription == null) {
            log.error("No active subscription found for customer: {}", customerId);
            throw new PaymentException(StripeErrorCode.ACTIVE_SUBSCRIPTION_REQUIRED);
        }

        if (Boolean.TRUE.equals(subscription.getCancelAtPeriodEnd())) {
            log.warn("Subscription {} is already marked to cancel at period end.", subscription.getId());
//            throw new PaymentException(StripeErrorCode.SUBSCRIPTION_NOT_PENDING_CANCEL);
            subscriptionCurrentService.updateAutoRenewStatus(subscription.getId(), 0);
            return;
        }

        // Step 2: 设置订阅为在当前账单周期结束时取消
        Subscription cancelledSubscription = cancelSubscriptionDelay(subscription);
        log.info("subscription set to cancel at period end: {}", cancelledSubscription.getId());
        log.info("业务调用未来取消订阅（取消自动续费）结束：subscriptionId:{}, customerId:{}, user:{}", subscription.getId(), customerId, user);
    }

    @Override
    public void uncancelSubscriptionFuture(User user) {
        log.info("取消原来-业务调用未来取消订阅（取消自动续费）开始, user:{}", user);
        Stripe.apiKey = secretKey;

        StripeUserCustomer stripeUserCustomer = getStripeCustomer(user);
        String customerId = stripeUserCustomer.getCustomerId();

        // Step 1: 查询客户的激活订阅
        Subscription subscription = findActiveSubscription(customerId);
        if (subscription == null) {
            log.error("No active subscription found for customer: {}", customerId);
            throw new PaymentException(StripeErrorCode.ACTIVE_SUBSCRIPTION_REQUIRED);
        }
        // Step 2: 检查订阅是否标记为 `cancel_at_period_end`
        if (Boolean.FALSE.equals(subscription.getCancelAtPeriodEnd())) {
            log.warn("Subscription {} is not marked to cancel at period end.", subscription.getId());
//            throw new PaymentException(StripeErrorCode.SUBSCRIPTION_NOT_PENDING_CANCEL);
            subscriptionCurrentService.updateAutoRenewStatus(subscription.getId(), 1);
            return;
        }

        // Step 3: 检查是否已有查询（`active` 和 `not_started` 状态）状态的 SubscriptionSchedule，
        List<SubscriptionSchedule> existingSchedules = findAllRelevantSubscriptionSchedules(customerId);
        if (!existingSchedules.isEmpty()) {
            log.warn("Found existing subscription schedules for customer {}: {}", customerId, existingSchedules);
            throw new PaymentException(StripeErrorCode.HAVE_ACTIVE_OR_NOSTART_SUBSCRIPTION_SCHEDULE_TO_UNCANCEL);
        }

        // Step 4: 恢复订阅自动续费
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("cancel_at_period_end", false); // 取消延时取消订阅
            Subscription updatedSubscription = subscription.update(params);
            log.info("Successfully restored subscription: {}", updatedSubscription.toJson());
        } catch (StripeException e) {
            log.error("Failed to uncancel subscription for customer: {}", customerId, e);
            throw new PaymentException(StripeErrorCode.UNCANCEL_SUBSCRIPTION_ERROR, e);
        }
        subscriptionCurrentService.updateAutoRenewStatus(subscription.getId(), 1);
        log.info("取消原来-业务调用未来取消订阅（取消自动续费）结束：subscriptionId:{}, customerId:{}, user:{}", subscription.getId(), customerId, user);
    }

    @Override
    public SubscriptionScheduleDTO changeSubscriptionDelay(BuyItemDto buyItemDto, User user) {
        log.info("业务调用延时取消旧订阅并创建新订阅计划开始, buyItemDto:{}, user:{}", buyItemDto, user);
        Stripe.apiKey = secretKey;

        //目前只支持同时购买一种
        com.lx.pl.pay.paypal.model.vo.ProductItem stripeItem = buyItemDto.getStripeItems().get(0);
        StripeUserCustomer stripeUserCustomer = getStripeCustomer(user);

        // 判断是否跨平台支付
        crossPlatformPaymentCheck(stripeUserCustomer);

        //判断stripeItem中的type必须为plan，否者报错
        if (!PaymentType.PLAN.equals(stripeItem.getType())) {
            log.error("Invalid payment type: {}, user:{}", stripeItem.getType(), user.getLoginName());
            throw new PaymentException(StripeErrorCode.UNSUPPORTED_PAYMENT_TYPE);
        }
        String customerId = stripeUserCustomer.getCustomerId();

        // Step 1: 查询客户的激活订阅
        Subscription oldSubscription = findActiveSubscription(customerId);
        if (oldSubscription == null) {
            log.error("No active subscription found for customer: {}, user:{}", customerId, user.getLoginName());
            throw new PaymentException(StripeErrorCode.ACTIVE_SUBSCRIPTION_REQUIRED);
        }


        // Step 2: 检查是否已有active状态的 SubscriptionSchedule，
        List<SubscriptionSchedule> existingSchedules = findAllRelevantSubscriptionSchedules(customerId);
        for (SubscriptionSchedule schedule : existingSchedules) {
            if ("active".equals(schedule.getStatus())) {
                // 处理 active 状态的订阅计划（当前请求不能执行）
                log.error("发现 active 订阅计划: scheduleId:{} for customer: {}, user:{}", schedule.getId(), customerId, user.getLoginName());
                throw new PaymentException(StripeErrorCode.HAVE_ACTIVE_SUBSCRIPTION_SCHEDULE);
            }
        }

        // Step 3: 设置旧订阅为在当前账单周期结束时取消
        Subscription cancelledSubscription = cancelSubscriptionDelay(oldSubscription);
        log.info("Old subscription set to cancel at period end: {}", cancelledSubscription.getId());

        // Step 4: 检查是否已有not_started状态 SubscriptionSchedule，并取消它们
        for (SubscriptionSchedule schedule : existingSchedules) {
            if ("not_started".equals(schedule.getStatus())) {
                // 取消 not_started 状态的订阅计划
                cancelSubscriptionSchedule(schedule);
                log.info("取消 not_started 订阅计划: {}", schedule.getId());
            }

        }

        StripeProduct newStripeProduct = stripeProductService.getStripeProductByBuyItemDto(stripeItem);
        String newPriceId = newStripeProduct.getStripePriceId();

        // Step 5: 创建新的订阅计划，设置为在旧订阅结束后生效/
        // 延迟 20秒
        long startDate = cancelledSubscription.getCurrentPeriodEnd() + 20;
        SubscriptionSchedule newSchedule = createSubscriptionSchedule(customerId, newPriceId, startDate);
        log.info("New subscription schedule created: {}", newSchedule.getId());
        log.info("业务调用延时取消旧订阅并创建新订阅计划成功, buyItemDto:{}, user:{}", buyItemDto, user);
        return new SubscriptionScheduleDTO(
                newStripeProduct.getPlanLevel(),
                newStripeProduct.getPriceInterval(),
                startDate);
    }

    @Override
    public void checkInvoiceAndPayment() {
        Stripe.apiKey = secretKey;
        int pageSize = 1;
        Page<StripeInvoice> pageParam = new Page<StripeInvoice>().setCurrent(pageSize).setSize(1000);
        List<StripeInvoice> stripeInvoiceList = null;
        do {
            pageParam.setCurrent(pageSize);
            Page<StripeInvoice> page = stripeInvoiceService
                    .lambdaQuery()
                    .orderByDesc(StripeInvoice::getId)
                    .page(pageParam);
            stripeInvoiceList = page.getRecords();
            if (CollUtil.isEmpty(stripeInvoiceList)) {
                break;
            }
            for (StripeInvoice stripeInvoice : stripeInvoiceList) {
                String invoiceId = stripeInvoice.getInvoiceId();
                try {
                    Invoice invoice = Invoice.retrieve(invoiceId);
                    stripeInvoiceService.lambdaUpdate()
                            .eq(StripeInvoice::getInvoiceId, invoiceId)
                            .set(StripeInvoice::getStatus, invoice.getStatus())
                            .set(StripeInvoice::getAmountDue, invoice.getAmountDue())
                            .set(StripeInvoice::getAmountPaid, invoice.getAmountPaid())
                            .set(StripeInvoice::getAmountRemaining, invoice.getAmountRemaining())
                            .set(StripeInvoice::getAmountExcludingTax, invoice.getTotalExcludingTax())
                            .set(StripeInvoice::getPaymentIntentId, invoice.getPaymentIntent())
                            .set(StripeInvoice::getUpdateTime, LocalDateTime.now())
                            .update();
                    ThreadUtil.sleep(300);
                    log.info("checkInvoiceAndPayment invoice success, invoiceId:{}", invoiceId);
                } catch (Exception e) {
                    log.error("Failed to retrieve invoice: {}", invoiceId, e);
                }
            }
            pageSize++;
        } while (CollUtil.isNotEmpty(stripeInvoiceList));
        log.info("checkInvoiceAndPayment invoice success end");


        Stripe.apiKey = secretKey;
        pageSize = 1;
        Page<StripePaymentIntent> pageParamRequest = new Page<StripePaymentIntent>().setCurrent(pageSize).setSize(1000);
        List<StripePaymentIntent> stripePaymentIntentList = null;
        do {
            pageParamRequest.setCurrent(pageSize);
            Page<StripePaymentIntent> page = stripePaymentIntentService
                    .lambdaQuery()
                    .orderByDesc(StripePaymentIntent::getId)
                    .page(pageParamRequest);
            stripePaymentIntentList = page.getRecords();
            if (CollUtil.isEmpty(stripePaymentIntentList)) {
                break;
            }
            for (StripePaymentIntent stripePaymentIntent : stripePaymentIntentList) {
                String paymentIntentId = stripePaymentIntent.getPaymentIntentId();
                try {
                    PaymentIntent paymentIntent = PaymentIntent.retrieve(paymentIntentId);
                    StripeError lastPaymentError = paymentIntent.getLastPaymentError();
                    LambdaUpdateChainWrapper<StripePaymentIntent> set = stripePaymentIntentService.lambdaUpdate()
                            .eq(StripePaymentIntent::getPaymentIntentId, paymentIntentId)
                            .set(StripePaymentIntent::getStatus, paymentIntent.getStatus())
                            .set(StripePaymentIntent::getInvoiceId, paymentIntent.getInvoice())
                            .set(StripePaymentIntent::getAmount, paymentIntent.getAmount())
                            .set(StripePaymentIntent::getCurrency, paymentIntent.getCurrency())
                            .set(StripePaymentIntent::getAmountReceived, paymentIntent.getAmountReceived())
                            .set(StripePaymentIntent::getUpdateTime, LocalDateTime.now());
                    if (lastPaymentError != null) {
                        set.set(StripePaymentIntent::getErrorCode, lastPaymentError.getCode())
                                .set(StripePaymentIntent::getErrorMessage, lastPaymentError.getMessage())
                                .set(StripePaymentIntent::getErrorType, lastPaymentError.getType())
                                .set(StripePaymentIntent::getDeclineCode, lastPaymentError.getDeclineCode())
                                .set(StripePaymentIntent::getNetworkAdviceCode, lastPaymentError.getNetworkAdviceCode());
                    }
                    set.update();
                    log.info("checkInvoiceAndPayment paymentIntent success for {}", paymentIntentId);
                    ThreadUtil.sleep(300);
                } catch (Exception e) {
                    log.error("Failed to retrieve payment intent: {}", paymentIntentId, e);
                }
            }
            pageSize++;
        } while (CollUtil.isNotEmpty(stripePaymentIntentList));
        log.info("checkInvoiceAndPayment paymentIntent success end");
    }

    @Override
    public void cancelSubscriptionSchedule(User user) {
        log.info("业务调用取消未来计划开始, , user:{}", user);
        Stripe.apiKey = secretKey;

        StripeUserCustomer stripeUserCustomer = getStripeCustomer(user);
        String customerId = stripeUserCustomer.getCustomerId();

        // Step 1: 检查是否已有active状态的 SubscriptionSchedule，
        List<SubscriptionSchedule> existingSchedules = findAllRelevantSubscriptionSchedules(customerId);
        for (SubscriptionSchedule schedule : existingSchedules) {
            if ("active".equals(schedule.getStatus())) {
                // 处理 active 状态的订阅计划（当前请求不能执行）
                log.error("发现 active 订阅计划: scheduleId:{} for customer: {}, user:{}", schedule.getId(), customerId, user.getLoginName());
                throw new PaymentException(StripeErrorCode.HAVE_ACTIVE_SUBSCRIPTION_SCHEDULE_TO_CANCEL);
            }
        }

        // Step 2: 检查是否已有not_started状态 SubscriptionSchedule，并取消它们
        for (SubscriptionSchedule schedule : existingSchedules) {
            if ("not_started".equals(schedule.getStatus())) {
                // 取消 not_started 状态的订阅计划
                cancelSubscriptionSchedule(schedule);
                log.info("取消 not_started 订阅计划成功: {}", schedule.getId());
            }
        }
        log.info("业务调用取消未来计划结束, , user:{}", user);

    }


    @Override
    public SubscriptionSchedule findNotStartedSubscriptionSchedule(User user) {
        Stripe.apiKey = secretKey;
        StripeUserCustomer stripeUserCustomer = stripeCustomerService.getStripeCustomerByUserId(user.getId());
        if (stripeUserCustomer == null || StringUtils.isBlank(stripeUserCustomer.getCustomerId())) {
            return null;
        }
        String customerId = stripeUserCustomer.getCustomerId();

        // Step 2: 检查是否已有active状态的 SubscriptionSchedule，
        List<SubscriptionSchedule> existingSchedules = findAllRelevantSubscriptionSchedules(customerId);
        for (SubscriptionSchedule schedule : existingSchedules) {
            if ("not_started".equals(schedule.getStatus())) {
                return schedule;
            }
        }
        return null;
    }


    @Override
    public Customer createCustomer(String email, String name) throws StripeException {
        Stripe.apiKey = secretKey;

        CustomerCreateParams params =
                CustomerCreateParams.builder()
                        .setEmail(email)
                        .setName(name)
                        .build();
        Customer customer = Customer.create(params);
        return customer;
    }


    // 获取或创建 StripeCustomer
    private StripeUserCustomer getOrCreateStripeCustomer(User user) {
        StripeUserCustomer stripeUserCustomer = stripeCustomerService.getStripeCustomerByUserId(user.getId());

        if (stripeUserCustomer == null) {
            try {
                Customer customer = createCustomer(user.getEmail(), user.getUserName());
                stripeUserCustomer = StripeUserCustomer.builder()
                        .userId(user.getId())
                        .loginName(user.getLoginName())
                        .email(user.getEmail())
                        .customerId(customer.getId())
                        .build();
                stripeCustomerService.save(stripeUserCustomer);
            } catch (StripeException e) {
                log.error("Failed to create customer. user:{}", user, e);
                throw new PaymentException(StripeErrorCode.CREATE_STRIPE_CUSTOMER_ERROR);
            }
        }

        return stripeUserCustomer;
    }

    // 获取 StripeCustomer
    private StripeUserCustomer getStripeCustomer(User user) {
        StripeUserCustomer stripeUserCustomer = stripeCustomerService.getStripeCustomerByUserId(user.getId());
        if (stripeUserCustomer == null) {
            log.error("User {} userid:{} does not have stripe customer", user.getLoginName(), user.getId());
            throw new PaymentException(StripeErrorCode.NO_STRIPE_CUSTOMER_FOUND);
        }
        return stripeUserCustomer;
    }

    // 处理订阅类型的支付（plan）
    private Session handlePlanPayment(BuyItemDto buyItemDto, com.lx.pl.pay.paypal.model.vo.ProductItem stripeItem, StripeUserCustomer stripeUserCustomer) {
        crossPlatformPaymentCheck(stripeUserCustomer);
        validateProduct(stripeItem);
        // 确保用户只有一个有效的订阅
        Subscription activeSubscription = findActiveSubscription(stripeUserCustomer.getCustomerId());
        if (activeSubscription != null) {
            log.error("User {} already has an active subscription", stripeUserCustomer.getLoginName());
            throw new PaymentException(StripeErrorCode.API_USAGE_ERROR);
        }

        StripeProduct stripeProduct = stripeProductService.getStripeProductByBuyItemDto(stripeItem);
        //订阅的时候数量只能是1
        stripeItem.setAmount(1);
        CheckoutSessionRequest checkoutSessionRequest = buildCheckoutSessionRequest(buyItemDto, stripeItem, stripeUserCustomer, stripeProduct, SessionCreateParams.Mode.SUBSCRIPTION);

        return createCheckoutSession(checkoutSessionRequest, stripeProduct, stripeUserCustomer.getUserId());
    }

    // 处理一次性支付（one）
    private Session handleOneTimePayment(BuyItemDto buyItemDto, StripeUserCustomer stripeUserCustomer) {
        List<ProductItem> stripeItems = buyItemDto.getStripeItems();

        checkOneTimeParam(stripeItems, stripeUserCustomer);
        Map<Integer, ProductItem> stripeItemMap = new HashMap<>();

        List<Integer> lumenList = new ArrayList<>();
        for (ProductItem stripeItem : stripeItems) {
            stripeItemMap.put(stripeItem.getLumen(), stripeItem);
            lumenList.add(stripeItem.getLumen());
        }

        List<StripeProduct> stripeProducts = stripeProductService.getOneTimeProductListByLumenQty(lumenList);

        CheckoutSessionRequest checkoutSessionRequest = buildCheckoutSessionRequest(buyItemDto, stripeUserCustomer, SessionCreateParams.Mode.PAYMENT);

        return createCheckoutSessionForPayment(checkoutSessionRequest, stripeProducts, stripeItemMap, stripeUserCustomer.getUserId());
    }

    private void checkOneTimeParam(List<ProductItem> stripeItems, StripeUserCustomer stripeUserCustomer) {
        if (CollUtil.isEmpty(stripeItems)) {
            for (ProductItem stripeItem : stripeItems) {
                if (stripeItem.getAmount() == null || stripeItem.getAmount() <= 0) {
                    throw new BadRequestException("Invalid amount");
                }
            }
        }
    }

    // 验证产品信息是否有效
    private void validateProduct(com.lx.pl.pay.paypal.model.vo.ProductItem stripeItem) {
        if (StringUtil.isBlank(stripeItem.getProduct()) || StringUtil.isBlank(stripeItem.getPrice())) {
            throw new BadRequestException("Invalid product type or price");
        }
    }

    // 构建 CheckoutSessionRequest 对象
    private CheckoutSessionRequest buildCheckoutSessionRequest(BuyItemDto buyItemDto, ProductItem stripeItem, StripeUserCustomer stripeUserCustomer, StripeProduct stripeProduct, SessionCreateParams.Mode mode) {
        return CheckoutSessionRequest.builder().successUrl(buyItemDto.getSuccessUrl()).cancelUrl(buyItemDto.getCancelUrl()).count(stripeItem.getAmount() == null ? 1L : stripeItem.getAmount())
                .mode(mode).priceId(stripeProduct.getStripePriceId()).customerId(stripeUserCustomer.getCustomerId()).coupon(buyItemDto.getCouponVo()).build();
    }

    private CheckoutSessionRequest buildCheckoutSessionRequest(BuyItemDto buyItemDto, StripeUserCustomer stripeUserCustomer, SessionCreateParams.Mode mode) {
        return CheckoutSessionRequest.builder().successUrl(buyItemDto.getSuccessUrl()).cancelUrl(buyItemDto.getCancelUrl()).mode(mode).coupon(buyItemDto.getCouponVo())
                .customerId(stripeUserCustomer.getCustomerId()).build();
    }


    public SubscriptionSchedule createSubscriptionSchedule(String customerId, String newPriceId, Long startDate) {
        log.info("业务调用创建订阅计划开始：customerId:{}, newPriceId:{}, startDate:{}", customerId, newPriceId, startDate);
        // 创建订阅计划参数
        SubscriptionScheduleCreateParams.Phase.Item item = SubscriptionScheduleCreateParams.Phase.Item.builder()
                .setPrice(newPriceId)
                .setQuantity(1L)
                .build();
        SubscriptionScheduleCreateParams params = SubscriptionScheduleCreateParams.builder()
                .setCustomer(customerId) // 设置客户 ID
                .setStartDate(startDate) // 设置新订阅计划的开始时间
                //当 SubscriptionSchedule 结束时，订阅会继续保留为“活跃状态”，并且会根据订阅的价格和周期设置 继续自动扣费。
                .setEndBehavior(SubscriptionScheduleCreateParams.EndBehavior.RELEASE) // 计划结束后继续订阅
                .addPhase(
                        SubscriptionScheduleCreateParams.Phase.builder()
                                .addItem(item)
//                                .setIterations(1L) //优惠期结束后，订阅会变成正常活跃状态，按正常价格自动续费。 未设置时，阶段会无限期执行
                                .build()
                )
                .build();
        // 创建新的订阅计划
        try {
            SubscriptionSchedule subscriptionSchedule = SubscriptionSchedule.create(params);
            log.info("业务调用创建订阅计划成功：customerId:{}, newPriceId:{}, startDate:{}, subscriptionSchedule:{}", customerId, newPriceId, startDate, subscriptionSchedule.toJson());
            return subscriptionSchedule;
        } catch (StripeException e) {
            log.error("Failed to create subscription schedule, customerId:{}, newPriceId:{}, startDate:{}, error:{}", customerId, newPriceId, startDate, e);
            throw new PaymentException(StripeErrorCode.CREATE_SUBSCRIPTION_SCHEDULE_ERROR);
        }
    }

    // 取消当前订阅
    public Subscription cancelSubscriptionNow(String subscriptionId) throws StripeException {
        Subscription subscription = Subscription.retrieve(subscriptionId);
        // 取消订阅（立即取消）
        Subscription cancelledSubscription = subscription.cancel();
        log.info("业务调用立即取消订阅成功：" + cancelledSubscription.toJson());
        return cancelledSubscription;
    }

    // 取消当前订阅
    public Subscription cancelSubscriptionNow(Subscription subscription) throws StripeException {
        // 取消订阅（立即取消）
        Subscription cancelledSubscription = subscription.cancel();
        log.info("业务调用立即取消订阅成功：" + cancelledSubscription.toJson());
        return cancelledSubscription;
    }

    // 取消当前订阅
    public Subscription cancelSubscriptionDelay(Subscription subscription) {
//         取消订阅时，也可以指定取消订阅的时间点：
        Map<String, Object> params = new HashMap<>();
        params.put("cancel_at_period_end", true);  // 如果你想要在当前账单周期结束时取消订阅
        Subscription cancelledSubscription = null;
        try {
            cancelledSubscription = subscription.update(params);
        } catch (StripeException e) {
            log.error("延迟取消订阅失败，subscription{}", subscription.toJson(), e);
            throw new PaymentException(StripeErrorCode.CANCEL_SUBSCRIPTION_DELAY_ERROR);
        }
        subscriptionCurrentService.updateAutoRenewStatus(subscription.getId(), 0);
        log.info("业务调用延期取消订阅成功：" + cancelledSubscription.toJson());
        return cancelledSubscription;
    }

    // 创建新的订阅
    public Subscription createNewSubscription(String customerId, String priceId) throws StripeException {
        log.info("业务调用创建订阅开始：customerId:{}, priceId:{}", customerId, priceId);
        // 创建订阅的参数
        SubscriptionCreateParams params = SubscriptionCreateParams.builder()
                .setCustomer(customerId)
                .addItem(SubscriptionCreateParams.Item.builder()
                        .setPrice(priceId)
                        .build())
                .build();

        // Step 2: 如果默认支付方式为空，获取最早的支付方式并设置为默认
        Customer customer = Customer.retrieve(customerId);
        String defaultPaymentMethod = customer.getInvoiceSettings().getDefaultPaymentMethod();
        if (defaultPaymentMethod == null || defaultPaymentMethod.isEmpty()) {
            PaymentMethodCollection paymentMethods = PaymentMethod.list(
                    PaymentMethodListParams.builder()
                            .setCustomer(customerId)
                            .setType(PaymentMethodListParams.Type.CARD)
                            .build()
            );

            if (!paymentMethods.getData().isEmpty()) {
                // 获取最早的支付方式
                PaymentMethod earliestPaymentMethod = paymentMethods.getData().get(0);

                // 设置为客户的默认支付方式
                CustomerUpdateParams updateParams = CustomerUpdateParams.builder()
                        .setInvoiceSettings(CustomerUpdateParams.InvoiceSettings.builder()
                                .setDefaultPaymentMethod(earliestPaymentMethod.getId())
                                .build())
                        .build();
                Customer updatedCustomer = customer.update(updateParams);

                log.info("设置客户默认支付方式成功：customerId:{}, defaultPaymentMethod:{}", customerId, earliestPaymentMethod.getId());
            } else {
                log.error("The customer has no payment method saved, so a subscription cannot be created. customerId:{}", customerId);
                throw new PaymentException(StripeErrorCode.NO_PAYMENT_METHOD_SAVED);
            }
        }

        // 创建新的订阅
        Subscription newSubscription = Subscription.create(params);
        log.info("业务调用创建订阅成功：customerId:{}, priceId:{}, 订阅信息:{}", customerId, priceId, newSubscription.toJson());
        return newSubscription;
    }

    // 查询客户的激活订阅
    public Subscription findActiveSubscription(String customerId) {
        // 查询与客户相关的所有订阅
        SubscriptionListParams params = SubscriptionListParams.builder()
                .setCustomer(customerId) // 过滤特定客户的订阅
                .build();

        List<Subscription> subscriptions = null;
        try {
            subscriptions = Subscription.list(params).getData();
        } catch (StripeException e) {
            log.error("Failed to get active subscription：customerId:{}", customerId, e);
            throw new PaymentException(StripeErrorCode.GET_ACTIVE_SUBSCRIPTION_ERROR);
        }

        // 筛选状态为 active 的订阅
        Optional<Subscription> activeSubscription = subscriptions.stream()
                .filter(sub -> "active".equals(sub.getStatus()) || "trialing".equals(sub.getStatus())) // 只保留状态为 active 的订阅
                .findFirst(); // 获取第一个匹配的订阅

        // 返回匹配的订阅或 null
        return activeSubscription.orElse(null);
    }

    /**
     * 查询所有关联的激活和未开始的订阅计划（`active` 和 `not_started` 状态）。
     * not_started: 计划尚未开始。
     * active: 计划正在执行。
     * completed: 计划已完成。
     * released: 计划被提前释放。
     * canceled: 计划已取消.
     *
     * @param customerId 客户 ID
     * @return 相关的订阅计划列表
     * @throws StripeException 如果 Stripe API 调用失败
     */
    public List<SubscriptionSchedule> findAllRelevantSubscriptionSchedules(String customerId) {
        Map<String, Object> params = new HashMap<>();
        params.put("customer", customerId);

        // 获取所有订阅计划
        List<SubscriptionSchedule> schedules = null;
        try {
            schedules = SubscriptionSchedule.list(params).getData();
        } catch (StripeException e) {
            log.error("Failed to get subscription schedules: {}", e);
            throw new PaymentException(StripeErrorCode.GET_SUBSCRIPTION_SCHEDULES_ERROR);
        }

        // 筛选出状态为 active 或 not_started 的计划
        return schedules.stream()
                .filter(schedule -> "active".equals(schedule.getStatus()) || "not_started".equals(schedule.getStatus()))
                .collect(Collectors.toList());
    }

    //取消已有的 SubscriptionSchedule
    public void cancelSubscriptionSchedule(SubscriptionSchedule schedule) {
        try {
            schedule.cancel();
        } catch (StripeException e) {
            log.error("Failed to cancel subscription schedule: {}", schedule.toJson(), e);
            throw new PaymentException(StripeErrorCode.CANCEL_SUBSCRIPTION_SCHEDULE_ERROR);
        }
    }

    @Override
    public com.stripe.model.billingportal.Session createPortal(User user, String returnUrl) {
        log.info("生成账单开始, user:{}", user);
        Stripe.apiKey = secretKey;
        StripeUserCustomer stripeUserCustomerByLoginName = stripeCustomerService.getStripeCustomerByUserId(user.getId());
        if (stripeUserCustomerByLoginName == null) {
            log.error("Failed to create billing, User {} userid: {} does not have stripe customer", user.getLoginName(), user.getId());
            throw new PaymentException(StripeErrorCode.NO_STRIPE_CUSTOMER_FOUND);
        }
        com.stripe.param.billingportal.SessionCreateParams params =
                com.stripe.param.billingportal.SessionCreateParams.builder()
                        .setCustomer(stripeUserCustomerByLoginName.getCustomerId())
                        .setReturnUrl(returnUrl)
                        .build();

        com.stripe.model.billingportal.Session session = null;
        try {
            session = com.stripe.model.billingportal.Session.create(params);
        } catch (StripeException e) {
            log.error("Failed to create billing, stripe call failed. User {}", user.getLoginName(), e);
            throw new PaymentException(StripeErrorCode.FAILED_TO_GET_BILLING);
        }
        log.info("生成账单开始结束, user:{}，session:{}", user, session);
        return session;
    }

    public Session createCheckoutSession(CheckoutSessionRequest checkoutSessionRequest, StripeProduct stripeProduct, Long userId) {
        log.info("业务调用创建CheckoutSession开始：{}", checkoutSessionRequest);
        SessionCreateParams.Builder sessionParams = SessionCreateParams.builder()
                .addAllPaymentMethodType(List.of(SessionCreateParams.PaymentMethodType.CARD))
                .setMode(checkoutSessionRequest.getMode())
                .setSuccessUrl(checkoutSessionRequest.getSuccessUrl())
                .setCancelUrl(checkoutSessionRequest.getCancelUrl())
                .setCustomer(checkoutSessionRequest.getCustomerId())
                .setAutomaticTax(SessionCreateParams.AutomaticTax.builder()
                        // 启用自动税务计算
                        .setEnabled(true)
                        .build())
                .setCustomerUpdate(SessionCreateParams.CustomerUpdate.builder()
                        // 启用自动更新地址
                        .setAddress(SessionCreateParams.CustomerUpdate.Address.AUTO)
                        .build());

        SessionCreateParams.Mode mode = checkoutSessionRequest.getMode();

        addSubCouponIfNeed(userId, sessionParams, checkoutSessionRequest.getCoupon(), stripeProduct);

        // 添加商品项（line_items），包括价格、数量和税率
        SessionCreateParams.LineItem.Builder lineItemBuilder = SessionCreateParams.LineItem.builder()
                .setQuantity(checkoutSessionRequest.getCount())
                // 设置价格 ID
                .setPrice(checkoutSessionRequest.getPriceId());


        // 如果有税率，添加税率信息
        String taxRateId = checkoutSessionRequest.getTaxRateId();
        if (taxRateId != null && !taxRateId.isEmpty()) {
            // 设置税率
            lineItemBuilder.addTaxRate(taxRateId);
        }

        // 添加该商品项
        sessionParams.addLineItem(lineItemBuilder.build());

        try {
            SessionListParams build = SessionListParams.builder()
                    .setCustomer(checkoutSessionRequest.getCustomerId())
                    .setLimit(10L)
                    .setStatus(SessionListParams.Status.OPEN)
                    .build();
            SessionCollection list = Session.list(build);
            if (list != null && list.getData() != null && !list.getData().isEmpty()) {
                for (Session datum : list.getData()) {
                    if ("open".equals(datum.getStatus())) {
                        log.info("Sub Customer: {} 有未完成的订单，开始过期建新的订单 {}", checkoutSessionRequest.getCustomerId(), datum.getStatus());
                        datum.expire();
                    }
                }
            }
        } catch (StripeException e) {
            log.error("Customer: {} Failed to create checkout session:", checkoutSessionRequest.getCustomerId(), e);
        }
        // 创建 Session 并返回
        try {
            log.info("业务调用创建CheckoutSession结束：{}", checkoutSessionRequest);
            return Session.create(sessionParams.build());
        } catch (StripeException e) {
            log.error("Customer: {} Failed to create checkout session:", checkoutSessionRequest.getCustomerId(), e);
            throw new PaymentException(StripeErrorCode.CREATE_CHECKOUT_SESSION_ERROR);
        }
    }

    private void addSubCouponIfNeed(Long userId, SessionCreateParams.Builder sessionParams, CouponVo coupon, StripeProduct stripeProduct) {
        boolean withTrial = vipService.canTrail(userId);

        Pair<String, Integer> userMaxDiscount = vipService.getUserMaxDiscount(userId, withTrial, stripeProduct.getProductType(), stripeProduct.getFirstBuySubDiscount());

        String promotionType = userMaxDiscount.getLeft();
        if (coupon != null) {
            if (coupon.getPercentOff() < userMaxDiscount.getRight()) {
                coupon = payCouponService.queryStripeCouponCodeByOff(userMaxDiscount.getRight());
            } else {
                promotionType = PromotionType.COUPON.getCode();
            }
        } else {
            coupon = payCouponService.queryStripeCouponCodeByOff(userMaxDiscount.getRight());
        }

        if (coupon != null) {
            sessionParams.addDiscount(SessionCreateParams.Discount.builder().setCoupon(coupon.getCode())
                    .build());
            log.info("业务调用创建CheckoutSession sub 优惠券 coupon：{} {}", coupon.getCode(), coupon.getName());

        }


        int trialDay = stripeProduct.getTrialDay() == null ? 0 : stripeProduct.getTrialDay();
        if (PromotionType.FIRST_BUY_SUB.getCode().equals(promotionType) && trialDay > 0) {
            SessionCreateParams.SubscriptionData.Builder builder = SessionCreateParams.SubscriptionData.builder();
            if (coupon != null) {
                if (coupon.getPayCouponCode() != null) {
                    builder.putAllMetadata(Map.of(PAY_COUPON_CODE_META_KEY, coupon.getPayCouponCode()));
                }
                if (coupon.getPercentOff() != null) {
                    builder.putAllMetadata(Map.of(OFF_META_KEY, coupon.getPercentOff().toString()));
                }
                builder.putAllMetadata(Map.of(PROMOTION_TYPE_META_KEY, promotionType));
            }

            SessionCreateParams.SubscriptionData subscriptionData = builder
                    .setTrialPeriodDays(Long.valueOf(stripeProduct.getTrialDay()))
                    .setTrialSettings(SessionCreateParams.SubscriptionData.TrialSettings.builder()
                            .setEndBehavior(SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.builder()
                                    .setMissingPaymentMethod(SessionCreateParams.SubscriptionData.TrialSettings.EndBehavior.MissingPaymentMethod.CANCEL)
                                    .build())
                            .build())
                    .build();
            sessionParams.setSubscriptionData(subscriptionData);
        } else {
            SessionCreateParams.SubscriptionData.Builder builder = SessionCreateParams.SubscriptionData.builder();
            if (coupon != null) {
                if (coupon.getPayCouponCode() != null) {
                    builder.putAllMetadata(Map.of(PAY_COUPON_CODE_META_KEY, coupon.getPayCouponCode()));
                }
                if (coupon.getPercentOff() != null) {
                    builder.putAllMetadata(Map.of(OFF_META_KEY, coupon.getPercentOff().toString()));
                }
                builder.putAllMetadata(Map.of(PROMOTION_TYPE_META_KEY, promotionType));
            }

            sessionParams.setSubscriptionData(builder.build());
        }
    }


    public Session createCheckoutSessionForPayment(CheckoutSessionRequest checkoutSessionRequest, List<StripeProduct> stripeProducts, Map<Integer, ProductItem> stripeItemMap, Long userId) {
        log.info("业务调用创建CheckoutSession payment开始：{}", checkoutSessionRequest);
        SessionCreateParams.Builder sessionParams = SessionCreateParams.builder()
                .addAllPaymentMethodType(Arrays.asList(SessionCreateParams.PaymentMethodType.CARD))
                .setMode(checkoutSessionRequest.getMode())
                .setSuccessUrl(checkoutSessionRequest.getSuccessUrl())
                .setCancelUrl(checkoutSessionRequest.getCancelUrl())
                .setCustomer(checkoutSessionRequest.getCustomerId())
                .setAutomaticTax(SessionCreateParams.AutomaticTax.builder()
                        .setEnabled(true)  // 启用自动税务计算
                        .build())
                .setCustomerUpdate(SessionCreateParams.CustomerUpdate.builder()
                        .setAddress(SessionCreateParams.CustomerUpdate.Address.AUTO) // 启用自动更新地址
                        .build());

        SessionCreateParams.Mode mode = checkoutSessionRequest.getMode();


        for (StripeProduct stripeProduct : stripeProducts) {
            Integer lumen = stripeProduct.getLumen();
            ProductItem stripeItem = stripeItemMap.get(lumen);

            // 添加商品项（line_items），包括价格、数量和税率
            SessionCreateParams.LineItem.Builder lineItemBuilder = SessionCreateParams.LineItem.builder()
                    // 设置价格 ID
                    .setPrice(stripeProduct.getStripePriceId())
                    .setQuantity(Long.valueOf(stripeItem.getAmount()));
            // 如果有税率，添加税率信息
            String taxRateId = checkoutSessionRequest.getTaxRateId();
            if (taxRateId != null && !taxRateId.isEmpty()) {
                // 设置税率
                lineItemBuilder.addTaxRate(taxRateId);
            }
            // 添加该商品项
            sessionParams.addLineItem(lineItemBuilder.build());
        }
        SessionCreateParams.InvoiceCreation.InvoiceData invoiceData = addCouponIfNeed(userId, sessionParams, checkoutSessionRequest.getCoupon());

        SessionCreateParams.InvoiceCreation invoiceCreation = SessionCreateParams.InvoiceCreation.builder()
                .setEnabled(true)
                .setInvoiceData(invoiceData)
                .build();
        // "payment"，然后设置 InvoiceCreation
        sessionParams.setInvoiceCreation(invoiceCreation);
        try {
            SessionListParams build = SessionListParams.builder()
                    .setCustomer(checkoutSessionRequest.getCustomerId())
                    .setLimit(10L)
                    .setStatus(SessionListParams.Status.OPEN)
                    .build();
            SessionCollection list = Session.list(build);
            if (list != null && list.getData() != null && !list.getData().isEmpty()) {
                for (Session datum : list.getData()) {
                    if ("open".equals(datum.getStatus())) {
                        log.info("Customer: {} 有未完成的订单，开始过期建新的订单 {}", checkoutSessionRequest.getCustomerId(), datum.getStatus());
                        datum.expire();
                    }
                }
            }
        } catch (StripeException e) {
            log.error("Customer: {} Failed to create checkout session", checkoutSessionRequest.getCustomerId(), e);
        }

        // 创建 Session 并返回
        try {
            log.info("业务调用创建CheckoutSession one 结束：{}", checkoutSessionRequest);
            return Session.create(sessionParams.build());
        } catch (StripeException e) {
            log.error("Customer: {} Failed to create checkout session one:", checkoutSessionRequest.getCustomerId(), e);
            throw new PaymentException(StripeErrorCode.CREATE_CHECKOUT_SESSION_ERROR);
        }
    }

    private SessionCreateParams.InvoiceCreation.InvoiceData addCouponIfNeed(Long userId, SessionCreateParams.Builder sessionParams, CouponVo couponUser) {
        Pair<String, Integer> userMaxDiscount = vipService.getUserMaxDiscount(userId, false, null, null);
        String leftPromotionType = userMaxDiscount.getLeft();
        Integer off = userMaxDiscount.getRight();
        if (off != null && off > 0 && (couponUser == null || off > couponUser.getPercentOff())) {
            ///  比较折扣 哪个折扣大曲哪个
            couponUser = payCouponService.queryStripeCouponCodeByOff(off);
        } else {
            leftPromotionType = PromotionType.COUPON.getCode();
        }
        if (couponUser == null) {
            log.info("没有找到对应的优惠券 ");
            return null;
        }
        off = couponUser.getPercentOff();

        log.info("业务调用创建CheckoutSession one 优惠券 couponUser：{} {}", couponUser.getCode(), couponUser.getName());
        sessionParams.addDiscount(SessionCreateParams.Discount.builder().setCoupon(couponUser.getCode())
                .build());
        SessionCreateParams.InvoiceCreation.InvoiceData.Builder builder = SessionCreateParams.InvoiceCreation.InvoiceData.builder();
        if (couponUser.getPayCouponCode() != null) {
            builder.putAllMetadata(Map.of(PAY_COUPON_CODE_META_KEY, couponUser.getPayCouponCode()));
        }
        if (off != null) {
            builder.putAllMetadata(Map.of(OFF_META_KEY, off.toString()));
        }
        builder.putAllMetadata(Map.of(PROMOTION_TYPE_META_KEY, leftPromotionType));
        return builder.build();
    }

    /**
     * @Description: 判断旧的订阅是否可以升级为新的订阅
     * @Param: [oldStripeProduct, newStripeProduct]
     * @return: boolean 如果可以升级返回true，否则返回false
     * @Author: senlin_he
     * @Date: 2024/12/27
     */
    public Boolean canUpgradeSubscription(StripeProduct oldStripeProduct, StripeProduct newStripeProduct) {
        // 提取 old 和 new 的相关字段
        String oldPlanLevel = oldStripeProduct.getPlanLevel();
        String oldPriceInterval = oldStripeProduct.getPriceInterval();
        String newPlanLevel = newStripeProduct.getPlanLevel();
        String newPriceInterval = newStripeProduct.getPriceInterval();

        // 允许的情况
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("standard") &&
                oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
            return true; // 从 standard month 到 standard year
        }
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") &&
                oldPriceInterval.equals("month") && newPriceInterval.equals("month")) {
            return true;
            // 从 standard month 到 pro month
        }
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") &&
                oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
            return true;
            // 从 standard month 到 pro year
        }
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") &&
                oldPriceInterval.equals("year") && newPriceInterval.equals("year")) {
            return true;
            // 从 standard year 到 pro year
        }
        if (oldPlanLevel.equals("pro") && newPlanLevel.equals("pro") &&
                oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
            return true;
            // 从 pro month 到 pro year
        }
//        if (oldPlanLevel.equals("pro") && newPlanLevel.equals("standard") &&
//                oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
//            return true; // 从 pro month 到 standard year
//        }

        // 不符合规则
        log.error("不支持的计划级别或价格间隔组合");
        return false;
    }

    /**
     * @Description: 判断旧的订阅是否可以升级为新的订阅
     * @Param: [oldStripeProduct, newStripeProduct]
     * @return: boolean 如果可以升级返回true，否则返回false
     * @Author: senlin_he
     * @Date: 2024/12/27
     */
    public Pair<Boolean, Boolean> canUpgradeSubscriptionByDefined(StripeProduct oldStripeProduct, StripeProduct newStripeProduct) {
        // 提取 old 和 new 的相关字段
        String oldPlanLevel = oldStripeProduct.getPlanLevel();
        String oldPriceInterval = oldStripeProduct.getPriceInterval();
        String newPlanLevel = newStripeProduct.getPlanLevel();
        String newPriceInterval = newStripeProduct.getPriceInterval();

        // 允许的情况
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("standard") &&
                oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
            return Pair.of(true, false); // 从 standard month 到 standard year
        }
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") &&
                oldPriceInterval.equals("month") && newPriceInterval.equals("month")) {
            return Pair.of(true, false);
            // 从 standard month 到 pro month
        }
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") &&
                oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
            return Pair.of(true, false);
            // 从 standard month 到 pro year
        }
        if (oldPlanLevel.equals("standard") && newPlanLevel.equals("pro") &&
                oldPriceInterval.equals("year") && newPriceInterval.equals("year")) {
            return Pair.of(true, false);
            // 从 standard year 到 pro year
        }
        if (oldPlanLevel.equals("pro") && newPlanLevel.equals("pro") &&
                oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
            return Pair.of(true, false);
            // 从 pro month 到 pro year
        }
//        if (oldPlanLevel.equals("pro") && newPlanLevel.equals("standard") &&
//                oldPriceInterval.equals("month") && newPriceInterval.equals("year")) {
//            return true; // 从 pro month 到 standard year
//        }

        // 不符合规则
        log.error("不支持的计划级别或价格间隔组合");
        return Pair.of(false, false);
    }

    public void crossPlatformPaymentCheck(StripeUserCustomer stripeUserCustomer) {
        Boolean canPay = subscriptionCurrentService.canPay(stripeUserCustomer.getUserId(), VipPlatform.STRIPE.getPlatformName());
        if (!canPay) {
            log.error("User {} Cannot make cross-platform payments，at this time{}", stripeUserCustomer.getLoginName(), System.currentTimeMillis());
            throw new PaymentException(StripeErrorCode.CROSS_PLATFORM_ERROR);
        }
    }

    private void updateSubscriptionItemPrice(SubscriptionItem subscriptionItem, String newPriceId) throws StripeException {
        log.info("业务调用升级订阅updateSubscriptionItemPrice开始：{}，{}", subscriptionItem.toJson(), newPriceId);
        Stripe.apiKey = secretKey;
        // 构建更新参数
        SubscriptionItemUpdateParams params =
                SubscriptionItemUpdateParams.builder()
                        .setPrice(newPriceId)
                        .setProrationBehavior(SubscriptionItemUpdateParams.ProrationBehavior.ALWAYS_INVOICE)
                        .build();

        // 更新订阅项
        SubscriptionItem subscriptionItemNew = subscriptionItem.update(params);
        log.info("业务调用升级订阅updateSubscriptionItemPrice结束,Updated subscription item：{}", subscriptionItemNew.toJson());
    }


    private void updateSubscriptionForItemPrice(Subscription resource, SubscriptionItem subscriptionItem, String newPriceId) throws StripeException {
        log.info("业务调用升级订阅 updateSubscriptionItemPrice 开始：{}，{}", resource.toJson(), newPriceId);
        Stripe.apiKey = secretKey;
        Long trialEnd = resource.getTrialEnd();
        // trialend 大于当前时间戳，则取消trial
        if (trialEnd != null && trialEnd > System.currentTimeMillis() / 1000) {
            log.info("取消trial now");
            SubscriptionUpdateParams params =
                    SubscriptionUpdateParams.builder()
                            .addItem(
                                    SubscriptionUpdateParams.Item.builder()
                                            .setId(subscriptionItem.getId())
                                            .setPrice(newPriceId)
                                            .setDiscounts(EmptyParam.EMPTY)
                                            .build()
                            )
                            .setTrialEnd(SubscriptionUpdateParams.TrialEnd.NOW)
                            .setProrationBehavior(SubscriptionUpdateParams.ProrationBehavior.ALWAYS_INVOICE)
                            .setDiscounts(EmptyParam.EMPTY)
                            .setCancelAtPeriodEnd(false)
                            .build();
            Subscription subscription = resource.update(params);
            log.info("业务调用升级订阅 updateSubscriptionItemPrice 结束,Updated subscription item：{}", subscription.toJson());
        } else {
            SubscriptionUpdateParams params =
                    SubscriptionUpdateParams.builder()
                            .addItem(
                                    SubscriptionUpdateParams.Item.builder()
                                            .setId(subscriptionItem.getId())
                                            .setPrice(newPriceId)
                                            .setDiscounts(EmptyParam.EMPTY)
                                            .build()
                            )
                            .setCancelAtPeriodEnd(false)
                            .setDiscounts(EmptyParam.EMPTY)
                            .setProrationBehavior(SubscriptionUpdateParams.ProrationBehavior.ALWAYS_INVOICE)
                            .build();
            Subscription subscription = resource.update(params);
            log.info("业务调用升级订阅 updateSubscriptionItemPrice 结束,Updated subscription item：{}", subscription.toJson());
        }
    }

}
