package com.lx.pl.pay.paypal.service.strategy;

import cn.hutool.core.util.StrUtil;
import com.lx.pl.enums.AlarmEnum;
import com.lx.pl.pay.common.service.PayCouponLogService;
import com.lx.pl.pay.paypal.annotation.PaypalEvent;
import com.lx.pl.pay.paypal.model.PaypalPaymentSaleModel;
import com.lx.pl.pay.paypal.model.domain.PayPalSubPaymentRecord;
import com.lx.pl.pay.paypal.model.event.PaypalPaymentSaleEvent;
import com.lx.pl.pay.paypal.service.IPaypalEventHandler;
import com.lx.pl.service.message.DingTalkAlert;
import com.lx.pl.util.LogicUtil;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 *
 */
@Component
@PaypalEvent(eventType = {"PAYMENT.SALE.COMPLETED", "PAYMENT.SALE.PENDING", "PAYMENT.SALE.DENIED", "PAYMENT.SALE.REVERSED"})
public class PaypalPaymentSaleHandler extends IPaypalEventHandler<PaypalPaymentSaleEvent> {
    @Resource
    private DingTalkAlert dingTalkAlert;
    @Resource
    private PayCouponLogService payCouponLogService;

    @Override
    public void handleEvent(PaypalPaymentSaleEvent data) {
        PaypalPaymentSaleModel saleModel = data.getModel();
        String billingAgreementId = saleModel.getBillingAgreementId();
        String id = saleModel.getId();
        String lockId = billingAgreementId == null ? id : billingAgreementId;
//        PayPalLogicSubscription payPalLogicSubscription = applicationContext.getBean(PayPalLogicSubscriptionService.class).queryBySubscriptionId(billingAgreementId);
//        if (payPalLogicSubscription != null) {
//            lockId= payPalLogicSubscription.getUserId().toString();
//        }
//        customId == null ? subscription.getId() : entries.getStr("userId"))
        log.info("lock key {}", PAYPAL_ACTION_LOCK_PREFIX + lockId);
        RLock lock = redissonClient.getLock(PAYPAL_ACTION_LOCK_PREFIX + lockId);
        String loginName = null;
        try {
            lock.lock();
            loginName = applicationContext.getBean(PaypalPaymentSaleHandler.class).doHandleEvent(data);
            if (StrUtil.isNotBlank(loginName)) {
                vipService.resettingPersonalLumens(loginName);
            }
        } finally {
            unlockAndRefreshVip(lock, loginName);
        }
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public String doHandleEvent(PaypalPaymentSaleEvent data) {
        log.info("doHandleEvent: {}", data.toJSON());
        if (!Objects.equals(data.getEventType(), "PAYMENT.SALE.COMPLETED")) {
            //发送告警信息
            dingTalkAlert.sendAlarmMessage(LogicUtil.buildAlarmMessage(
                    AlarmEnum.AlarmTypeEnum.BUSINESS.getDescription(),
                    AlarmEnum.AlarmSourceEnum.PAY_PAL.getDescription(),
                    "PayPal payment sale alarm, id: " + data.getId() + ", event: " + data.getEventType(),
                    LogicUtil.getUserIdByPayPalEvent(data),
                    null));
        }
        switch (data.getEventType()) {
            case "PAYMENT.SALE.COMPLETED":
                log.info("PAYMENT.SALE.COMPLETED: {}", data.getModel().toJSON());
                return handleCompletedPayment(data);
            case "PAYMENT.SALE.PENDING":
                log.info("PAYMENT.SALE.PENDING:{} {}", data.getModel().getBillingAgreementId(), data.getModel().toJSON());
                // 通知 管理员检查是否到账会员
                return null;
            case "PAYMENT.SALE.DENIED":
            case "PAYMENT.SALE.REFUNDED":
            case "PAYMENT.SALE.REVERSED":
                return handleReversedPayment(data);
            default:
                log.info("PAYMENT.SALE.OTHER: {}", data.getModel().toJSON());
                // todo ADD MSG
                return null;
        }

    }

    private String handleReversedPayment(PaypalPaymentSaleEvent data) {
        PayPalSubPaymentRecord payPalSubPaymentRecord = payPalSubPaymentRecordService.queryByPaymentId(data.getModel().getId());
        if (payPalSubPaymentRecord != null) {
            payPalSubPaymentRecordService.lambdaUpdate().eq(PayPalSubPaymentRecord::getId, payPalSubPaymentRecord.getId()).set(PayPalSubPaymentRecord::getState, data.getEventType())
                    .set(PayPalSubPaymentRecord::getReasonCode, data.getModel().getReasonCode()).set(PayPalSubPaymentRecord::getUpdateTime, LocalDateTime.now()).update();
        } else {
            log.info("PAYMENT.SALE.REVERSED: {} 不存在", data.getModel().getId());
            payPalSubPaymentRecordService.saveRecordIfNeed(data.getModel());
            return null;
        }
        return null;
    }

    private String handleCompletedPayment(PaypalPaymentSaleEvent data) {
        PaypalPaymentSaleModel saleModel = data.getModel();
        PayPalSubPaymentRecord payPalSubPaymentRecord = payPalSubPaymentRecordService.saveRecordIfNeed(saleModel);
        if (payPalSubPaymentRecord != null) {
            // 记录优惠券使用日志
            payCouponLogService.savePaypalSubLogIfNeed(payPalSubPaymentRecord, saleModel.getCustom());

            // 计算会员逻辑
            log.info("start calculateVipByPayment");
            return payPalLogicSubscriptionService.calculateVipByPayment(payPalSubPaymentRecord);
        }
        return null;
    }
}
