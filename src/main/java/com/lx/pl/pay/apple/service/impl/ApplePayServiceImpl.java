package com.lx.pl.pay.apple.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.apple.itunes.storekit.client.AppStoreServerAPIClient;
import com.apple.itunes.storekit.client.GetTransactionHistoryVersion;
import com.apple.itunes.storekit.model.HistoryResponse;
import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.apple.itunes.storekit.model.TransactionHistoryRequest;
import com.apple.itunes.storekit.model.Type;
import com.apple.itunes.storekit.verification.SignedDataVerifier;
import com.apple.itunes.storekit.verification.VerificationException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lx.pl.db.mysql.gen.entity.PayAppleProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PayAppleProductMapper;
import com.lx.pl.enums.AppleErrorCode;
import com.lx.pl.exception.PayAppleException;
import com.lx.pl.pay.apple.VipPlatform;
import com.lx.pl.pay.apple.domain.AppleJWSTransaction;
import com.lx.pl.pay.apple.domain.PayAppleUserRelation;
import com.lx.pl.pay.apple.dto.PayAppleProductDto;
import com.lx.pl.pay.apple.dto.ValidateRequest;
import com.lx.pl.pay.apple.service.*;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.pay.common.util.PayLogContextHolder;
import com.lx.pl.service.UserService;
import com.lx.pl.service.VipService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.apple.itunes.storekit.model.TransactionHistoryRequest.ProductType.AUTO_RENEWABLE;
import static com.apple.itunes.storekit.model.TransactionHistoryRequest.ProductType.CONSUMABLE;
import static com.lx.pl.constant.LockPrefixConstant.TRANSACTION_ID_LOCK_PREFIX;
import static com.lx.pl.enums.AppleErrorCode.*;

@Service
@RequiredArgsConstructor
public class ApplePayServiceImpl implements ApplePayService {

    Logger log = LoggerFactory.getLogger("apple-pay-msg");
    private final PayAppleProductMapper payAppleProductMapper;
    private final AppStoreServerAPIClient appStoreServerAPIClient;
    private final SignedDataVerifier signedPayloadVerifier;
    private final PayAppleUserRelationService payAppleUserRelationService;
    private final AppleJWSTransactionService appleTransactionService;
    private final AppleJWSRenewalInfoService appleRenewalInfoService;
    private final PayApplePurchaseRecordService payApplePurchaseRecordService;
    private final SubscriptionCurrentService subscriptionCurrentService;
    private final UserService userService;
    private final PayAppleProductService payAppleProductService;
    private final PayLumenRecordService payLumenRecordService;
    private final RedissonClient redissonClient;
    private final ApplicationContext applicationContext;
    private final PayAppleUpgradeLogService payAppleUpgradeLogService;
    private final VipService vipService;
    private final PayAppleRestoreLogService payAppleStoreLogService;
    private final MongoTemplate mongoTemplate;

    @Override
    public List<PayAppleProductDto> getProduct() {
        // 查询指定字段，直接从实体类中获取字段名
        List<PayAppleProduct> products = payAppleProductMapper.selectList(new LambdaQueryWrapper<PayAppleProduct>()
                .eq(PayAppleProduct::getStatus, 1)
                .select(PayAppleProduct::getAppleProductId,
                        PayAppleProduct::getLumen,
                        PayAppleProduct::getInitialLumen,
                        PayAppleProduct::getPlanLevel,
                        PayAppleProduct::getProductType,
                        PayAppleProduct::getMark,
                        PayAppleProduct::getPriceInterval));

        // 使用 Stream API 将结果转换为 DTO
        return products.stream().map(product -> PayAppleProductDto.builder()
                .appleProductId(product.getAppleProductId())
                .lumen(product.getLumen())
                .initialLumen(product.getInitialLumen())
                .planLevel(product.getPlanLevel())
                .productType(product.getProductType())
                .priceInterval(product.getPriceInterval())
                .mark(product.getMark())
                .build()).collect(Collectors.toList());
    }

    @Override
    public boolean restore(ValidateRequest request, User user) {
        JWSTransactionDecodedPayload payload = verifyAndDecodeTransaction(request.getSignPayload());
        log.info("restore start: {} {}", payload.getTransactionId(), request);
        // 根据通知类型处理
        String lockKey = TRANSACTION_ID_LOCK_PREFIX + payload.getOriginalTransactionId();
        RLock lock = redissonClient.getLock(lockKey);
        PayAppleUserRelation userRelationSrc = null;
        try {
            lock.lock();
            Pair<PayAppleUserRelation, Boolean> payAppleUserRelationBooleanPair = applicationContext.getBean(ApplePayServiceImpl.class)
                    .restoreLogic(user, payload, true);
            userRelationSrc = payAppleUserRelationBooleanPair.getLeft();
        } finally {
            try {
                if (userRelationSrc != null) {
                    log.info("restore end userRelationSrc: {}", userRelationSrc.getLoginName());
                    vipService.resettingPersonalLumens(userRelationSrc.getLoginName());
                    updateUserVipStatus(userRelationSrc.getUserId());
                    subscriptionCurrentService.clearUserCache(userRelationSrc.getUserId());
                }
                vipService.resettingPersonalLumens(user.getLoginName());
                updateUserVipStatus(user.getId());
                subscriptionCurrentService.clearUserCache(user.getId());

                log.info("restore end: {}", user.getLoginName());
            } catch (Exception e) {
                log.error("restore error: {}", e.getMessage(), e);
            }
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
        return false;
    }

    @Override
    public boolean validateReceiptV2(ValidateRequest request, User user) {
        JWSTransactionDecodedPayload payload = verifyAndDecodeTransaction(request.getSignPayload());
        log.info("validateReceiptV2 start loginName={} transactionId={} {}", user.getLoginName(), payload.getTransactionId(), payload);
        // 根据通知类型处理
        String lockKey = TRANSACTION_ID_LOCK_PREFIX + payload.getOriginalTransactionId();
        RLock lock = redissonClient.getLock(lockKey);
        PayAppleUserRelation userRelationSrc = null;
        try {
            lock.lock();
            Pair<PayAppleUserRelation, Boolean> payAppleUserRelationBooleanPair = applicationContext.getBean(ApplePayServiceImpl.class)
                    .restoreLogic(user, payload, false);
            userRelationSrc = payAppleUserRelationBooleanPair.getLeft();
            Boolean occurredRestore = payAppleUserRelationBooleanPair.getRight();
            if (!occurredRestore && userRelationSrc != null) {
                user = userService.getUserById(userRelationSrc.getUserId());
                log.warn("validateReceiptV2 restore end userRelationSrc changed: {} {}", userRelationSrc.getLoginName(), user.getLoginName());
            }
            boolean rtn = applicationContext.getBean(ApplePayServiceImpl.class).doValidateReceiptV2(user, payload);
            return rtn;
        } finally {
            try {
                if (userRelationSrc != null) {
                    log.info("validateReceiptV2 end userRelationSrc: {}", userRelationSrc.getLoginName());
                    vipService.resettingPersonalLumens(userRelationSrc.getLoginName());
                    updateUserVipStatus(userRelationSrc.getUserId());
                    subscriptionCurrentService.clearUserCache(userRelationSrc.getUserId());
                }
                vipService.resettingPersonalLumens(user.getLoginName());
                updateUserVipStatus(user.getId());
                subscriptionCurrentService.clearUserCache(user.getId());
                log.info("validateReceiptV2 end: {}", user.getLoginName());
                saveToMongoLog(user, payload);
            } catch (Exception e) {
                log.error("validateReceiptV2 error", e);
            }
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
            log.info(" validateReceiptV2 end loginName={} transactionId={} {}", user.getLoginName(), payload.getTransactionId(), payload);
        }
    }

    private void saveToMongoLog(User user, JWSTransactionDecodedPayload payload) {
        try {
            Map<String, Object> logData = new HashMap<>();
            logData.put("uuid", PayLogContextHolder.getLogUUID());
            logData.put("transaction", payload);
            logData.put("type", "VALIDATE_RECEIPT");  // 添加业务类型标识
            logData.put("createTime", LocalDateTime.now()
                    .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));    // 添加时间戳
            logData.put("userId", user.getId());      // 添加用户信息
            logData.put("loginName", user.getLoginName());

            // 创建更清晰的数据结构
            Map<String, Object> transactionInfo = new HashMap<>();
            transactionInfo.put("transactionId", payload.getTransactionId());
            transactionInfo.put("originalTransactionId", payload.getOriginalTransactionId());
            transactionInfo.put("productId", payload.getProductId());
            transactionInfo.put("purchaseDate", payload.getPurchaseDate());
            logData.put("transactionInfo", transactionInfo);
            // 指定集合名称并处理异常
            mongoTemplate.save(logData, "apple_pay_logs");
        } catch (Exception e) {
            log.error("saveToMongoLog error", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Pair<PayAppleUserRelation, Boolean> restoreLogic(User user, JWSTransactionDecodedPayload payload, Boolean isRestore) {
        PayAppleUserRelation userRelationNow = payAppleUserRelationService.getOrCreatePayAppleUserRelation(user);

        if (userRelationNow == null) {
            // 没有关联信息 数据不对
            throw new PayAppleException(TRANSACTION_RELATION_ERROR);
        }
        PayAppleUserRelation userRelationSrc = payAppleUserRelationService.lambdaQuery()
                .eq(PayAppleUserRelation::getOriginalTransactionId, payload.getOriginalTransactionId())
                .eq(PayAppleUserRelation::getValid, true).one();

        PayAppleUserRelation originRelation = payAppleUserRelationService.lambdaQuery()
                .eq(PayAppleUserRelation::getLoginName, user.getLoginName()).eq(PayAppleUserRelation::getValid, true)
                .one();

        // 判断relation 的userId 是否与 当前userId 相同
        boolean needRestore = userRelationSrc != null && !userRelationSrc.getUserId()
                .equals(user.getId()) && (originRelation == null || originRelation.getOriginalTransactionId() == null);
        if (needRestore) {
            log.info("用户: {} 恢复购买: transactionId={}, originalTransactionId={}", user.getId(), payload.getTransactionId(), payload.getOriginalTransactionId());
            // 更改relation 关系
            payAppleUserRelationService.lambdaUpdate().eq(PayAppleUserRelation::getId, userRelationSrc.getId())
                    .set(PayAppleUserRelation::getOriginalTransactionId, null)
                    .set(PayAppleUserRelation::getUpdateTime, LocalDateTime.now()).update();
            payAppleUserRelationService.lambdaUpdate().eq(PayAppleUserRelation::getId, userRelationNow.getId())
                    .set(PayAppleUserRelation::getOriginalTransactionId, userRelationSrc.getOriginalTransactionId())
                    .set(PayAppleUserRelation::getUpdateTime, LocalDateTime.now()).update();
            // 把原始relation 相关的购买记录 restore 到新用户
            subscriptionCurrentService.restoreSubscriptionRecord(userRelationSrc.getOriginalTransactionId(), userRelationSrc.getUserId(), user.getId(), user.getLoginName());
            payLumenRecordService.restoreLumenByUserId(userRelationSrc.getOriginalTransactionId(), user.getId(), user.getLoginName());
            // 记录恢复购买日志
            payAppleStoreLogService.logRestore(userRelationSrc.getId(), userRelationSrc.getLoginName(), userRelationNow.getId(), userRelationNow.getLoginName(), payload.getOriginalTransactionId(), payload.getTransactionId());
        } else {
            log.info("用户: {} 恢复购买: transactionId={}, originalTransactionId={},用户相同 不需要恢复", user.getId(), payload.getTransactionId(), payload.getOriginalTransactionId());
            Long userIdSrc = userRelationSrc != null ? userRelationSrc.getUserId() : null;
            boolean isRestoreSelf = user.getId().equals(userIdSrc);
            if (isRestoreSelf) {
                log.info("用户: {} 恢复购买: transactionId={}, originalTransactionId={},用户相同 不需要恢复", user.getLoginName(), payload.getTransactionId(), payload.getOriginalTransactionId());
            }
            if (isRestore != null && isRestore && !isRestoreSelf) {
                throw new PayAppleException(CAN_NOT_RESTORE);
            }
        }
        return Pair.of(userRelationSrc, needRestore);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean doValidateReceiptV2(User user, JWSTransactionDecodedPayload payload) {
        String transactionId = payload.getTransactionId();
        String originalTransactionId = payload.getOriginalTransactionId();
        boolean isInitialBuy = originalTransactionId.equals(transactionId);
        log.info("处理苹果支付验证开始: userId={}, transactionId={}, originalTransactionId={}, isInitialBuy={}", user.getId(), payload.getTransactionId(), payload.getOriginalTransactionId(), isInitialBuy);
        AppleErrorCode errorCode = null;
        if (appleTransactionService.existsByTransactionIdWithUserId(transactionId)) {
            log.info("交易已处理: userId={}, transactionId={}", user.getId(), transactionId);
            errorCode = REPEAT_VALIDATE_ERROR;
        } else {
            switch (payload.getType()) {
                case AUTO_RENEWABLE_SUBSCRIPTION:
                    handleAutoRenewableSubscription(payload, user, isInitialBuy);
                    break;
                case NON_CONSUMABLE:
                    handleNonConsumable(payload, user);
                    break;
                case CONSUMABLE:
                    handleConsumable(payload, user);
                    break;
                case NON_RENEWING_SUBSCRIPTION:
                    handleNonRenewingSubscription(payload, user, isInitialBuy);
                    break;
                default: {
                    log.warn("未知的交易类型: type={}", payload.getType());
                }
            }
        }
        if (errorCode != null) {
            throw new PayAppleException(errorCode);
        }
//            updateUserVipStatus(user.getId());
        log.info("处理苹果支付验证完成: userId={}, transactionId={}", user.getId(), transactionId);
        return true;
    }

    private JWSTransactionDecodedPayload verifyAndDecodeTransaction(String signPayload) {
        try {
            return signedPayloadVerifier.verifyAndDecodeTransaction(signPayload);
        } catch (VerificationException e) {
            log.error("验证和解码交易失败: signPayload={}", signPayload, e);
            throw new PayAppleException(VERIFY_AND_DECODE_TRANSACTION_ERROR, e);
        }
    }

    private boolean handleAutoRenewableSubscription(JWSTransactionDecodedPayload payload, User user, boolean isInitialBuy) {
        log.info("处理自动续订订阅: userId={}, transactionId={}, isInitialBuy={}", user.getId(), payload.getTransactionId(), isInitialBuy);
        if (isInitialBuy) {
            PayAppleProduct product = getAndValidateProduct(payload.getProductId());
            return handleInitialPurchase(payload, user, product);
        } else {
            return handleRenewalPurchase(payload, user);
        }
    }

    private PayAppleProduct getAndValidateProduct(String productId) {
        PayAppleProduct product = payAppleProductMapper.selectOne(new QueryWrapper<PayAppleProduct>()
                .eq("status", 1).
                eq("apple_product_id", productId));

        if (product == null) {
            log.error("商品不存在: productId={}", productId);
            throw new PayAppleException(PRODUCT_NOT_FOUND);
        }
        return product;
    }

    @Override
    public boolean handleInitialPurchase(JWSTransactionDecodedPayload payload, User user, PayAppleProduct product) {
        String transactionId = payload.getTransactionId();
        // 检查是否已处理
        if (appleTransactionService.existsByTransactionId(transactionId)) {
            return true;
        }
        // 保存相关记录
        saveInitialPurchaseRecords(payload, user, product);

//        // 更新用户VIP信息
//        updateUserVipStatus(user.getId());

        log.info("首次购买订阅处理完成: userId={}, lumen={}", user.getId(), product.getLumen());
        return true;
    }

    private boolean isTransactionProcessed(String transactionId, String originalTransactionId) {
        if (appleTransactionService.existsByTransactionId(transactionId)) {
            PayAppleUserRelation relation = payAppleUserRelationService.lambdaQuery()
                    .eq(PayAppleUserRelation::getOriginalTransactionId, originalTransactionId).one();
            if (relation != null) {
                log.info("该购买交易已处理: transactionId={}", transactionId);
                return true;
            }
        }
        return false;
    }

    private void saveInitialPurchaseRecords(JWSTransactionDecodedPayload payload, User user, PayAppleProduct product) {
        appleTransactionService.saveTransaction(payload, user);
        payAppleUserRelationService.updateOriginalTransactionId(user, payload.getOriginalTransactionId());

        boolean successPurchase = payApplePurchaseRecordService.saveAppleLogicPurchaseRecord(payload, user, Type.AUTO_RENEWABLE_SUBSCRIPTION);
        if (successPurchase) {
            subscriptionCurrentService.saveOrUpdateAppleSubscriptionCurrent(buildAppleSubscriptionCurrent(payload, user, product));
        }
    }

    private boolean handleRenewalPurchase(JWSTransactionDecodedPayload payload, User user) {
        List<String> transactionsHistory = getTransactionHistory(payload, AUTO_RENEWABLE);
        if (CollUtil.isEmpty(transactionsHistory)) {
            log.info("无新交易需要处理: transactionId={}", payload.getTransactionId());
            return true;
        }

        Map<String, String> existingTransactions = getExistingTransactions(payload, Type.AUTO_RENEWABLE_SUBSCRIPTION);
        payAppleUserRelationService.queryRelationByOriginalTransactionId(payload, () -> this.getTransactionHistory(payload, AUTO_RENEWABLE));
        processNewTransactions(transactionsHistory, existingTransactions, user);
//        updateUserVipStatus(user.getId());
        return true;
    }

    private Map<String, String> getExistingTransactions(JWSTransactionDecodedPayload payload, Type type) {
        List<AppleJWSTransaction> transactions = appleTransactionService.querySubscriptionListByOriginalTransactionId(payload.getOriginalTransactionId());

        return transactions.stream()
                .collect(Collectors.toMap(AppleJWSTransaction::getTransactionId, AppleJWSTransaction::getOriginalTransactionId));
    }

    private void processNewTransactions(List<String> transactions, Map<String, String> existingTransactions, User user) {
        for (String signedTransaction : transactions) {
            JWSTransactionDecodedPayload transactionPayload = verifyAndDecodeTransaction(signedTransaction);

            if (existingTransactions.containsKey(transactionPayload.getTransactionId())) {
                log.info("该交易已处理: transactionId={}", transactionPayload.getTransactionId());
                continue;
            }
            // 订阅已过期 不处理
            if (transactionPayload.getExpiresDate() != null && transactionPayload.getExpiresDate() < System.currentTimeMillis()) {
                appleTransactionService.saveTransaction(transactionPayload, user);
                log.info("该交易已过期: transactionId={}", transactionPayload.getTransactionId());
                continue;
            }
            saveRenewalPurchaseRecords(transactionPayload, user);
            log.info("续订交易处理完成: transactionId={}", transactionPayload.getTransactionId());
        }
    }

    private void saveRenewalPurchaseRecords(JWSTransactionDecodedPayload payload, User user) {
        appleTransactionService.saveTransaction(payload, user);
        boolean successPurchase = payApplePurchaseRecordService.saveAppleLogicPurchaseRecord(payload, user, Type.AUTO_RENEWABLE_SUBSCRIPTION);

        if (successPurchase) {
            SubscriptionCurrent currentSubscription = subscriptionCurrentService.queryByOriginTransactionId(payload.getOriginalTransactionId());
            if (currentSubscription == null) {
                log.error("未找到当前订阅信息: userId={}, originalTransactionId:{}", user.getId(), payload.getOriginalTransactionId());
                throw new PayAppleException(SUBSCRIPTION_NOT_FOUND);
            }
            log.info("当前用户续费订阅: {} {} {}", currentSubscription.getTransactionId(), currentSubscription.getPlanLevel(), currentSubscription.getPriceInterval());
            String lastTransactionId = currentSubscription.getTransactionId();
            AppleJWSTransaction appleJWSTransaction = appleTransactionService.queryByTransactionId(lastTransactionId);
            PayAppleProduct payAppleProduct = payAppleProductService.queryByProductId(appleJWSTransaction.getProductId());
            PayAppleProduct newProduct = payAppleProductService.queryByProductId(payload.getProductId());
            if (payAppleProductService.isUpdate(payAppleProduct, newProduct)) {
                log.info("用户升级订阅: {} {}", currentSubscription.getLoginName(), currentSubscription.getSubscriptionId());
                subscriptionCurrentService.updateSubscriptionPlan(currentSubscription, newProduct.getAppleProductId(), payload);
                String oldProductId = payApplePurchaseRecordService.recallLumenByTransactionId(lastTransactionId);
                // 记录产品升级日志
                payAppleUpgradeLogService.logUpgrade(user.getId(), user.getLoginName(), payload.getProductId(), oldProductId, payload.getOriginalTransactionId(), payload.getTransactionId());
            }
            subscriptionCurrentService.saveOrUpdateAppleSubscriptionCurrent(buildAppleSubscriptionCurrent(payload, user, newProduct));
            log.info("保存逻辑订阅成功: userId={}, transactionId={}", user.getId(), payload.getTransactionId());
        }
    }

    private void updateUserVipStatus(Long userId) {
        SubscriptionCurrent subscription = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(userId);
        log.info("更新用户VIP等级 start: loginName={} newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
        userService.updateUserVipInfo(subscription, userId);
        log.info("更新用户VIP等级 end: loginName={}, newPlanLevel={} priceInterval={}", subscription.getLoginName(), subscription.getPlanLevel(), subscription.getPriceInterval());
    }

    @Override
    public List<String> getTransactionHistory(JWSTransactionDecodedPayload payload, TransactionHistoryRequest.ProductType productType) {
        TransactionHistoryRequest request = new TransactionHistoryRequest().sort(TransactionHistoryRequest.Order.ASCENDING)
                .revoked(false).productTypes(List.of(productType));

        List<String> transactions = new LinkedList<>();
        HistoryResponse response = null;

        do {
            try {
                response = appStoreServerAPIClient.getTransactionHistory(payload.getOriginalTransactionId(), response != null ? response.getRevision() : null, request, GetTransactionHistoryVersion.V2);
                transactions.addAll(response.getSignedTransactions());
            } catch (Exception e) {
                log.error("获取交易历史失败: originalTransactionId={}", payload.getOriginalTransactionId(), e);
                throw new PayAppleException(GET_HISTORY_ERROR, e);
            }
        } while (response.getHasMore());

        if (CollUtil.isEmpty(transactions)) {
            log.warn("交易历史为空: originalTransactionId={}", payload.getOriginalTransactionId());
            return Collections.emptyList();
        }

        return transactions;
    }

    protected static SubscriptionCurrent buildAppleSubscriptionCurrent(JWSTransactionDecodedPayload payload, User user, PayAppleProduct product) {
        // 改成setter
        SubscriptionCurrent build = new SubscriptionCurrent();
        build.setUserId(user.getId());
        build.setTransactionId(payload.getTransactionId());
        build.setOriginalTransactionId(payload.getOriginalTransactionId());
        build.setLoginName(user.getLoginName());
        build.setSubscriptionId(payload.getTransactionId());
        build.setVipPlatform(VipPlatform.IOS.getPlatformName());
        long now = System.currentTimeMillis();
        Long purchaseDate = payload.getPurchaseDate();
        if (product.getMark() == null) {
            build.setMark("v1");
        } else {
            build.setMark(product.getMark());
        }
        build.setCurrentPeriodStart(now > purchaseDate ? purchaseDate / 1000 : now / 1000);
        build.setCurrentPeriodEnd(payload.getExpiresDate() / 1000);
        build.setPlanLevel(product.getPlanLevel());
        build.setPriceInterval(product.getPriceInterval());
        build.setRenewPrice(product.getPrice());
        build.setAutoRenewStatus(1);
        build.setInvalid(false);
        return build;
    }

    private boolean handleConsumable(JWSTransactionDecodedPayload payload, User user) {
        // 实现消耗型商品处理逻辑
        log.info("处理消耗型商品: userId={}, transactionId={}", user.getId(), payload.getTransactionId());
        // 1. 查询商品信息

        PayAppleProduct product = getAndValidateProduct(payload.getProductId());
        if (product == null) {
            log.error("商品不存在: productId={}", payload.getProductId());
            throw new PayAppleException(PRODUCT_NOT_FOUND);
        }
        List<String> transactionsHistory = getTransactionHistory(payload, CONSUMABLE);
        if (CollUtil.isEmpty(transactionsHistory)) {
            log.info("该交易已处理: transactionId={}", payload.getTransactionId());
            return true;
        }
        for (String signedTransaction : transactionsHistory) {
            try {
                JWSTransactionDecodedPayload transactionPayload = signedPayloadVerifier.verifyAndDecodeTransaction(signedTransaction);
                // 检查交易是否已处理
                if (appleTransactionService.existsByTransactionIdWithUserId(transactionPayload.getTransactionId())) {
                    log.info("该交易已处理: transactionId={}", transactionPayload.getTransactionId());
                    continue;
                }

                String transactionId = transactionPayload.getTransactionId();
                AppleJWSTransaction appleJWSTransaction = appleTransactionService.queryByTransactionId(transactionId);
                if (appleJWSTransaction != null && appleJWSTransaction.getUserId() == null) {
                    // 保存购买记录
                    appleTransactionService.updateTransaction(transactionPayload, user);
                    payApplePurchaseRecordService.saveAppleLogicPurchaseRecord(payload, user, Type.CONSUMABLE);
                } else {
                    // 保存并处理新交易
                    // 保存交易记录并处理首次购买
                    appleTransactionService.saveTransaction(transactionPayload, user);
                    payApplePurchaseRecordService.saveAppleLogicPurchaseRecord(payload, user, Type.CONSUMABLE);
                    log.info("消耗型商品处理完成: transactionId={}", transactionPayload.getTransactionId());
                }
            } catch (VerificationException e) {
                log.error("验证交易签名失败", e);
                throw new PayAppleException(VERIFY_AND_DECODE_TRANSACTION_ERROR, e);
            }
        }
        log.info("消耗型商品购买成功，发放Lumen: userId={}, lumen={}", user.getId(), product.getLumen());
        return true;

    }

    private boolean handleNonConsumable(JWSTransactionDecodedPayload payload, User user) {
        log.info("处理非消耗型商品: userId={}, transactionId={}", user.getId(), payload.getTransactionId());
        // 实现非消耗型商品处理逻辑
        return true;
    }

    private boolean handleNonRenewingSubscription(JWSTransactionDecodedPayload payload, User user, boolean isInitialBuy) {
        log.info("处理非续期订阅: userId={}, transactionId={}, isInitialBuy={}", user.getId(), payload.getTransactionId(), isInitialBuy);
        // 实现非续期订阅处理逻辑
        return true;
    }


}
