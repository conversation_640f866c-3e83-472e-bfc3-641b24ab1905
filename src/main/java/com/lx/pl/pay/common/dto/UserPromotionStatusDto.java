package com.lx.pl.pay.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "用户优惠状态DTO")
public class UserPromotionStatusDto {

//    /**
//     * 是否能试用
//     */
//    @Schema(description = "是否能试用")
//    private Boolean canTrial;

//    /**
//     * 是否有首充优惠
//     */
//    @Schema(description = "是否有首充优惠")
//    private Boolean hasFirstBuyOffer;

//    /**
//     * 是否是老VIP用户回归
//     */
//    @Schema(description = "是否是老VIP用户回归")
//    private Boolean isOldVipReturn;

    /**
     * 全站优惠信息
     */
    @Schema(description = "全站优惠信息")
    private PromotionInfo planPromotion;
    /**
     * 全站优惠信息
     */
    @Schema(description = "全站优惠信息")
    private PromotionInfo lumenPromotion;

    @Schema(description = "是否是首充lumen")
    private Boolean firstBuyLumen;

    /**
     * 优惠后的价格列表
     */
    @Schema(description = "优惠后的价格列表")
    private List<ProductPriceInfo> discountedPrices;

    private String planLevel;

    private String priceInterval;


    @Data
    @Schema(description = "优惠信息")
    public static class PromotionInfo {

        public PromotionInfo() {
            this.type = "none";
            this.off = 0;
        }

        /**
         * 优惠类型
         */
        @Schema(description = "优惠类型")
        private String type;

//        /**
//         * 优惠名称
//         */
//        @Schema(description = "优惠名称")
//        private String name;

        /**
         * 折扣百分比
         */
        @Schema(description = "折扣百分比")
        private Integer off;
    }

    @Data
    @Schema(description = "产品价格信息")
    public static class ProductPriceInfo {
        /**
         * 计划等级
         */
        @Schema(description = "计划等级")
        private String planLevel;

        /**
         * 产品类型
         */
        @Schema(description = "产品类型")
        private String productType;

        /**
         * 价格间隔
         */
        @Schema(description = "价格间隔")
        private String priceInterval;

        /**
         * 原价
         */
        @Schema(description = "原价")
        private String originalPrice;

        /**
         * 折扣后价格
         */
        @Schema(description = "折扣后价格")
        private String discountedPrice;

        @Schema(description = "折扣百分比")
        private Integer off;

        /**
         * 节省金额
         */
        @Schema(description = "节省金额")
        private String savedAmount;

        /**
         * 应用的优惠类型
         */
        @Schema(description = "应用的优惠类型")
        private String appliedPromotionType;

        /**
         * lumen 折扣 类型
         */
        @Schema(description = "lumen 折扣 类型")
        private String lumenDiscountType;

        /**
         * Lumen数量
         */
        @Schema(description = "Lumen数量")
        private Integer lumen;

        /**
         * 初始Lumen数量（试用）
         */
        @Schema(description = "初始Lumen数量（试用）")
        private Integer initialLumen;

        /**
         * 试用天数
         */
        @Schema(description = "试用天数")
        private Integer trialDay;

        /**
         * 商品描述
         */
        @Schema(description = "商品描述")
        private String mark;


    }
}
