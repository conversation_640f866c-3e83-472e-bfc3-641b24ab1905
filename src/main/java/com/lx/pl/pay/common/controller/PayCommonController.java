package com.lx.pl.pay.common.controller;

import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.generic.R;
import com.lx.pl.pay.apple.dto.PayAppleProductDto;
import com.lx.pl.pay.apple.service.ApplePayService;
import com.lx.pl.pay.common.domain.SubscriptionCurrent;
import com.lx.pl.pay.common.dto.SubscriptionCurrentDto;
import com.lx.pl.pay.common.dto.UserPromotionStatusDto;
import com.lx.pl.pay.common.service.CommonProductService;
import com.lx.pl.pay.common.service.PayLumenRecordService;
import com.lx.pl.pay.common.service.SubscriptionCurrentService;
import com.lx.pl.service.StripeProductService;
import com.lx.pl.service.VipService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Tag(name = "vip共通相关接口")
@RequestMapping("/api/pay/common")
public class PayCommonController {

    @Autowired
    VipService vipService;
    @Autowired
    SubscriptionCurrentService subscriptionCurrentService;
    @Autowired
    CommonProductService commonProductService;
    @Autowired
    StripeProductService stripeProductService;
    @Autowired
    ApplePayService applePayService;
    @Autowired
    PayLumenRecordService payLumenRecordService;


    @Operation(summary = "查询用户vip信息详情for pay")
    @Authorization
    @GetMapping(path = "/vip-info-for-pay")
    public R<Map<String, Object>> vipInfo(@Parameter(hidden = true) @CurrentUser User user, String vipPlatform) {
        return R.success(vipService.getRealUserVipInfo(user.getId(), vipPlatform));
    }

    @Operation(summary = "查询支付用所有激活vip信息详情")
    @Authorization
    @GetMapping(path = "/all-pay-vip-info")
    public R<List<SubscriptionCurrentDto>> getAllPayVipInfo(@Parameter(hidden = true) @CurrentUser User user) {
        List<SubscriptionCurrent> validSubscriptionsFromDb = subscriptionCurrentService.getValidSubscriptionsFromDb(user.getId());
        List<SubscriptionCurrentDto> subscriptionCurrentDtos = validSubscriptionsFromDb.stream()
                .map(sub -> {
                    SubscriptionCurrentDto dto = new SubscriptionCurrentDto();
                    dto.setCurrentPeriodStart(sub.getCurrentPeriodStart());
                    dto.setCurrentPeriodEnd(sub.getCurrentPeriodEnd());
                    dto.setPlanLevel(sub.getPlanLevel());
                    dto.setPriceInterval(sub.getPriceInterval());
                    dto.setVipBeginTime(sub.getVipBeginTime());
                    dto.setVipEndTime(sub.getVipEndTime());
                    dto.setVipPlatform(sub.getVipPlatform());
                    dto.setAutoRenewStatus(sub.getAutoRenewStatus());
                    dto.setMark(sub.getMark());
                    dto.setRenewPrice(sub.getRenewPrice());
                    dto.setTrial(sub.getTrial());
                    return dto;
                })
                .collect(Collectors.toList());
        // 按条件分组，保留每组中 vipEndTime 最大的数据
        Map<List<String>, Optional<SubscriptionCurrentDto>> groupedMap = subscriptionCurrentDtos.stream()
                .collect(Collectors.groupingBy(
                        dto -> Arrays.asList(dto.getVipPlatform(), dto.getPlanLevel(), dto.getPriceInterval()),
                        Collectors.maxBy(Comparator.comparing(SubscriptionCurrentDto::getVipEndTime))
                ));

        List<SubscriptionCurrentDto> filteredList = groupedMap.values().stream()
                .filter(Optional::isPresent) // 过滤掉空值
                .map(Optional::get)          // 提取实际值
                .collect(Collectors.toList());
        return R.success(filteredList.stream().sorted(Comparator.comparing((SubscriptionCurrentDto sub) -> {
            // 设置 vipType 的优先级，转换为整数
            switch (sub.getPlanLevel()) {
                case "pro":
                    return 3;    // pro 为最高
                case "standard":
                    return 2;    // standard 为中等
                case "basic":
                    return 1;    // basic 为最低
                default:
                    return 1;    // 未知类型
            }
        }).thenComparing(sub -> {
            // 设置 priceInterval 的优先级，年付 > 月付
            return "year".equals(sub.getPriceInterval()) ? 1 : 0;
        }).reversed()).collect(Collectors.toList()));


    }

//    @Operation(summary = "查询用户优惠状态")
//    @Authorization
//    @GetMapping(path = "/user-promotion-status")
//    public R<UserPromotionStatusDto> getUserPromotionStatus(@Parameter(hidden = true) @CurrentUser User user) {
//        UserPromotionStatusDto result = new UserPromotionStatusDto();
//        UserPromotionStatusDto.PromotionInfo globalPromotion = new UserPromotionStatusDto.PromotionInfo();
//        // 1. 检查用户是否能试用
//        Boolean canTrial = vipService.canTrail(user.getId());
//        if (canTrial) {
//            globalPromotion.setType("first_buy_sub");
//            globalPromotion.setName("首购优惠券");
//        } else if (subscriptionCurrentService.hasBoughtVipAndHasExpire(user.getId(), 0)) {
//            globalPromotion.setType("old_vip_back");
//            globalPromotion.setName("旧会员升级为VIP优惠");
//        } else {
//            globalPromotion.setType("none");
//            globalPromotion.setName("无优惠");
//        }
//
//        // 2. 检查用户是否有首充优惠
//        Boolean hasFirstBuyOffer = vipService.canFirstGift(user.getId());
//        result.setHasFirstBuyOffer(hasFirstBuyOffer);
//        result.setGlobalPromotion(globalPromotion);
//        return R.success(result);
//    }

    @Operation(summary = "查询用户优惠状态和折扣价格列表")
    @Authorization
    @GetMapping(path = "/user-promotion-status")
    public R<UserPromotionStatusDto> getUserPromotionStatus(@Parameter(hidden = true) @CurrentUser User user, @RequestParam(value = "platform", defaultValue = "stripe", required = false) String platform) {

        UserPromotionStatusDto result = new UserPromotionStatusDto();

//        // 1. 检查用户是否能试用
        Boolean canTrial = vipService.canTrail(user.getId());
//        result.setCanTrial(canTrial);
//
//        // 2. 检查用户是否有首充优惠
//        Boolean hasFirstBuyOffer = vipService.canFirstGift(user.getId());
//        result.setHasFirstBuyOffer(hasFirstBuyOffer);
//
//        // 3. 检查用户是否是老VIP用户回归（0天内过期的VIP用户）
//        Boolean isOldVipReturn = subscriptionCurrentService.hasBoughtVipAndHasExpire(user.getId(), 0);
//        result.setIsOldVipReturn(isOldVipReturn);

        // 4. 获取全站优惠信息（暂时为空，后续可扩展）
//        List<UserPromotionStatusDto.PromotionInfo> objects = new ArrayList<>();
//        // 取off 最大的一个全站优惠
//        if (!objects.isEmpty()) {
//            UserPromotionStatusDto.PromotionInfo globalPromotion = objects.stream().max(Comparator.comparingInt(UserPromotionStatusDto.PromotionInfo::getOff)).orElse(null);
//            result.setGlobalPromotion(globalPromotion);
//        }
//        List<VipStandards> vipStandardsList = vipService.getVipStandardsList(user);
//        Map<String, VipStandards> vipStandardMap = vipStandardsList.stream().collect(Collectors.toMap(VipStandards::getVipType, Function.identity()));
//
        SubscriptionCurrent logicValidHighSubscriptionsFromDb = subscriptionCurrentService.getLogicValidHighSubscriptionsFromDb(user.getId());
        if (logicValidHighSubscriptionsFromDb != null) {
            result.setPlanLevel(logicValidHighSubscriptionsFromDb.getPlanLevel());
            result.setPriceInterval(logicValidHighSubscriptionsFromDb.getPriceInterval());
        }
//        // 5. 获取产品价格列表并计算折扣
        UserPromotionStatusDto.PromotionInfo planPromotion = new UserPromotionStatusDto.PromotionInfo();
        UserPromotionStatusDto.PromotionInfo lumenPromotion = new UserPromotionStatusDto.PromotionInfo();
        List<UserPromotionStatusDto.ProductPriceInfo> discountedPrices = calculateDiscountedPrices(user, platform, canTrial, planPromotion, lumenPromotion);
        result.setDiscountedPrices(discountedPrices);
        result.setPlanPromotion(planPromotion);
        result.setLumenPromotion(lumenPromotion);
        result.setFirstBuyLumen(!payLumenRecordService.hasPurchasedLumen(user.getId()));
        return R.success(result);
    }

    /**
     * 计算折扣后的价格列表
     */
    private List<UserPromotionStatusDto.ProductPriceInfo> calculateDiscountedPrices(User user, String platform, Boolean canTrial, UserPromotionStatusDto.PromotionInfo planPromotion, UserPromotionStatusDto.PromotionInfo lumenPromotion) {

        List<UserPromotionStatusDto.ProductPriceInfo> priceInfoList = new ArrayList<>();

        if ("stripe".equalsIgnoreCase(platform)) {
            // 获取 Stripe 产品列表
            List<StripeProduct> stripeProducts = stripeProductService.list().stream().filter(product -> product.getStatus() != null && product.getStatus() == 1).collect(Collectors.toList());
            Map<String, Pair<String, Integer>> productTypeDiscountMap = new HashMap<>();
            for (StripeProduct product : stripeProducts) {
                UserPromotionStatusDto.ProductPriceInfo priceInfo = new UserPromotionStatusDto.ProductPriceInfo();
//                priceInfo.setProductId(product.getStripeProductId());
                priceInfo.setMark(product.getMark());
                priceInfo.setPlanLevel(product.getPlanLevel());
                priceInfo.setProductType(product.getProductType());
                priceInfo.setPriceInterval(product.getPriceInterval());
                priceInfo.setLumen(product.getLumen());
                priceInfo.setInitialLumen(product.getInitialLumen());
                priceInfo.setTrialDay(product.getTrialDay());
                priceInfo.setMark(product.getMark());

                String originalPrice = product.getPrice();
                priceInfo.setOriginalPrice(originalPrice);
                Pair<String, Integer> stringIntegerPair = productTypeDiscountMap.get(product.getProductType());
                if (stringIntegerPair == null) {
                    stringIntegerPair = vipService.getUserMaxDiscount(user.getId(), canTrial, product.getProductType(), product.getFirstBuySubDiscount());
                    productTypeDiscountMap.put(product.getProductType(), stringIntegerPair);
                    if ("plan".equals(product.getProductType()) && stringIntegerPair.getLeft() != null) {
                        planPromotion.setType(stringIntegerPair.getLeft());
                        planPromotion.setOff(stringIntegerPair.getRight());
                    } else {
                        if (stringIntegerPair.getLeft() != null)
                            lumenPromotion.setType(stringIntegerPair.getLeft());
                        lumenPromotion.setOff(stringIntegerPair.getRight());
                    }
                }

                // 计算折扣
                Triple<String, String, Integer> discountedPrice = calculateDiscount(originalPrice, product, productTypeDiscountMap);
                priceInfo.setDiscountedPrice(discountedPrice.getMiddle());
                priceInfo.setOff(discountedPrice.getRight());

                // 计算节省金额
                if (originalPrice != null && discountedPrice != null) {
                    try {
                        BigDecimal original = new BigDecimal(originalPrice);
                        BigDecimal discounted = new BigDecimal(discountedPrice.getMiddle());
                        BigDecimal saved = original.subtract(discounted);
                        priceInfo.setSavedAmount(saved.toString());
                    } catch (NumberFormatException e) {
                        priceInfo.setSavedAmount("0");
                    }
                } else {
                    priceInfo.setSavedAmount("0");
                }

                // 设置应用的优惠类型
//                String appliedPromotionType = getAppliedPromotionType(product, canTrial, isOldVipReturn, globalPromotions);
                priceInfo.setAppliedPromotionType(discountedPrice.getLeft());
                priceInfoList.add(priceInfo);
            }
        } else if ("apple".equalsIgnoreCase(platform)) {
            // 获取 Apple 产品列表
            List<PayAppleProductDto> appleProducts = applePayService.getProduct();

            for (PayAppleProductDto product : appleProducts) {
                UserPromotionStatusDto.ProductPriceInfo priceInfo = new UserPromotionStatusDto.ProductPriceInfo();
                priceInfo.setMark(product.getMark());
                priceInfo.setPlanLevel(product.getPlanLevel());
                priceInfo.setProductType(product.getProductType());
                priceInfo.setPriceInterval(product.getPriceInterval());
                priceInfo.setLumen(product.getLumen());
                priceInfo.setInitialLumen(product.getInitialLumen());
                priceInfo.setMark(product.getMark());

                // Apple 产品价格由客户端提供，这里暂时不计算折扣
                priceInfo.setOriginalPrice("0");
                priceInfo.setDiscountedPrice("0");
                priceInfo.setSavedAmount("0");
                priceInfo.setAppliedPromotionType("none");

                priceInfoList.add(priceInfo);
            }
        }

        return priceInfoList;
    }

    /**
     * 计算单个产品的折扣价格
     */
    private Triple<String, String, Integer> calculateDiscount(String originalPrice, StripeProduct product, Map<String, Pair<String, Integer>> productTypeDiscountMap) {
        if (originalPrice == null) {
            return Triple.of("none", "0", 0);
        }
        try {
            Pair<String, Integer> stringIntegerPair = productTypeDiscountMap.getOrDefault(product.getProductType(), Pair.of("none", 0));
            BigDecimal price = new BigDecimal(originalPrice);
            int maxDiscount = 0;
            String appliedPromotionType = stringIntegerPair.getLeft();
            Integer off = stringIntegerPair.getRight();
            // 应用最大折扣
            if (off > 0) {
                BigDecimal discountPercent = new BigDecimal(100 - off).divide(new BigDecimal(100), 8, RoundingMode.HALF_DOWN);
                price = price.multiply(discountPercent);
            }

            return Triple.of(appliedPromotionType, price.setScale(2, RoundingMode.HALF_DOWN).toString(), off);
        } catch (NumberFormatException e) {
            return Triple.of("none", originalPrice, 0);
        }

//        if ("plan".equals(product.getProductType())) {
//            try {
//                Pair<String, Integer> stringIntegerPair = productTypeDiscountMap.getOrDefault(product.getProductType(), Pair.of("none", 0));
//                BigDecimal price = new BigDecimal(originalPrice);
//                int maxDiscount = 0;
//                String appliedPromotionType = stringIntegerPair.getLeft();
//                Integer off = stringIntegerPair.getRight();
//
////                // 试用和老VIP回归是互斥的，优先级：试用 > 老VIP回归
////                if (canTrial && product.getFirstBuySubDiscount() != null && product.getFirstBuySubDiscount() > 0) {
////                    // 试用用户的订阅折扣
////                    maxDiscount = product.getFirstBuySubDiscount();
////                    appliedPromotionType = "first_buy_sub";
////                    off = product.getFirstBuySubDiscount();
////                } else if (isOldVipReturn && !canTrial) {
////                    // 老VIP用户回归优惠
////                    maxDiscount = product.getOldVipBack();
////                    appliedPromotionType = "old_vip_back";
////                    off = product.getOldVipBack();
////                }
//
////                // 与全站优惠比较，取折扣力度更大的
////                if (globalPromotion != null && globalPromotion.getOff() != null && globalPromotion.getOff() > maxDiscount) {
////                    maxDiscount = globalPromotion.getOff();
////                    appliedPromotionType = globalPromotion.getType();
////                    off = globalPromotion.getOff();
////                }
//
//                // 应用最大折扣
//                if (off > 0) {
//                    BigDecimal discountPercent = new BigDecimal(100 - off).divide(new BigDecimal(100), 8, RoundingMode.HALF_DOWN);
//                    price = price.multiply(discountPercent);
//                }
//
//                return Triple.of(appliedPromotionType, price.setScale(2, RoundingMode.HALF_DOWN).toString(), off);
//            } catch (NumberFormatException e) {
//                return Triple.of("none", originalPrice, 0);
//            }
//        } else {
//            Pair<String, Integer> stringIntegerPair = productTypeDiscountMap.getOrDefault(product.getProductType(), Pair.of("none", 0));
//            // 对于非plan类型产品（如one类型），使用VIP会员的lumen折扣
//            VipStandards vipStandards = vipStandardMap.get(planLevel);
//            if (vipStandards != null && vipStandards.getLumenDiscount() != null && vipStandards.getLumenDiscount() > 0) {
//                try {
//                    BigDecimal price = new BigDecimal(originalPrice);
//                    int maxDiscount = vipStandards.getLumenDiscount();
//                    Integer off = vipStandards.getLumenDiscount();
//                    String appliedPromotionType = "lumen_off";
//                    // 与全站优惠比较，取折扣力度更大的
//                    if (globalPromotion != null && globalPromotion.getOff() != null && globalPromotion.getOff() > maxDiscount) {
//                        maxDiscount = globalPromotion.getOff();
//                        appliedPromotionType = globalPromotion.getType();
//                        off = globalPromotion.getOff();
//                    }
//                    BigDecimal discountPercent = new BigDecimal(100 - maxDiscount).divide(new BigDecimal(100), 8, RoundingMode.HALF_DOWN);
//                    price = price.multiply(discountPercent);
//                    return Triple.of(appliedPromotionType, price.setScale(2, RoundingMode.HALF_DOWN).toString(), off);
//                } catch (NumberFormatException e) {
//                    return Triple.of("none", originalPrice, 0);
//                }
//            }
//            return Triple.of("none", originalPrice, 0);
//        }
    }

    /**
     * 获取应用的优惠类型
     */
//    private Pair<String, String> getAppliedPromotionType(StripeProduct product, Boolean canTrial, Boolean isOldVipReturn, UserPromotionStatusDto.PromotionInfo globalPromotion) {
//        if ("plan".equals(product.getProductType())) {
//            int productDiscount = 0;
//            String productPromotionType = "none";
//
//            // 确定产品自带的折扣
//            if (canTrial && product.getFirstBuySubDiscount() != null && product.getFirstBuySubDiscount() > 0) {
//                productDiscount = product.getFirstBuySubDiscount();
//                productPromotionType = "trial";
//            } else if (isOldVipReturn && !canTrial) {
//                productDiscount = product.getOldVipBack();
//                productPromotionType = "old_vip_return";
//            }
//
//            // 比较全站优惠
//            if (globalPromotion != null && globalPromotion.getOff() != null && globalPromotion.getOff() > productDiscount) {
//                return globalPromotion.getType();
//            }
//            return productPromotionType;
//        }
//        return "none";
//    }
}
