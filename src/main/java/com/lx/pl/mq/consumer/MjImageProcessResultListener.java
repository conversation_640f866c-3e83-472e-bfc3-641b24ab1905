package com.lx.pl.mq.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.lx.pl.dto.mq.MjImageProcessResultVo;
import com.lx.pl.mq.listener.BaseMessageListener;
import com.lx.pl.service.MjImageProcessResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * MJ图片处理结果消费者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.piclumen.topic:tp_piclumen_test}",
        consumerGroup = "${rocketmq.image.process.result.group:gid_image_process_test}",
        tag = "${rocketmq.image.process.result.tag:tag_image_process_result_test}",
        consumptionThreadCount = 4)
public class MjImageProcessResultListener extends BaseMessageListener {

    private static final Logger logger = LoggerFactory.getLogger("rocketmq-msg");

    @Resource
    private MjImageProcessResultService mjImageProcessResultService;

    @Override
    public void doWork(MessageView message) {
        try {
            logger.info("收到MJ图片处理结果消息id:{}", message.getMessageId());
            MjImageProcessResultVo resultVo = this.getBody(message, new TypeReference<MjImageProcessResultVo>() {
            });

            if (resultVo == null) {
                logger.error("消费MJ图片处理结果消息id:{},参数为空", message.getMessageId());
                return;
            }

            // 处理图片处理结果
            mjImageProcessResultService.handleImageProcessResult(resultVo);

            logger.info("消费MJ图片处理结果消息id:{},完成", message.getMessageId());
        } catch (Exception e) {
            logger.error("消费MJ图片处理结果消息id:{},报错", message.getMessageId(), e);
        }
    }
}
