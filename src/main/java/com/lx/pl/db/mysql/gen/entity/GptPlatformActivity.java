package com.lx.pl.db.mysql.gen.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.Value;
import org.springframework.data.annotation.Transient;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("gpt_platform_activity")
public class GptPlatformActivity extends MyBaseEntity {

    @TableId
    private Long id;

    /**
     * 标题信息
     */
    private String title;

    /**
     * 简介
     */
    private String introduction;


    /**
     * 详情
     */
    private String details;

    /**
     * 跳转地址
     */
    private String linkAddress;

    /**
     * 用户类型（vip: 会员, not_vip: 非会员, all: 所有用户）
     */
    private String userType;


    /**
     * 发布状态（0：未发布，1：已发布）
     */
    private Boolean publish;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    @TableField(exist = false)
    private Boolean read;
}
