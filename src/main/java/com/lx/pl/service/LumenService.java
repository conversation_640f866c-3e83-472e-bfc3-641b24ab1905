package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.dto.*;
import com.lx.pl.enums.LumenType;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.PublicType;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.pay.common.domain.PayLumenRecord;
import com.lx.pl.pay.common.mapper.PayLumenRecordMapper;
import com.lx.pl.util.DateUtils;
import com.lx.pl.util.DoubleMathUtils;
import com.lx.pl.util.JsonUtils;
import com.lx.pl.util.LogicUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.lx.pl.service.VipService.*;

/**
 * <AUTHOR>
 * @date 2025/6/5
 * @description lumen相关Service
 */
@Slf4j
@Service
public class LumenService {
    @Resource
    private RedisService redisService;
    @Resource
    private PromptRecordMapper promptRecordMapper;
    @Resource
    private PayLumenRecordMapper payLumenRecordMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private UserMapper userMapper;
    @Resource
    private ModelService modelService;
    public static final String USER_NOT_FINISH_TASK_LUMENS = "user_not_finish_task_lumens"; // 用户的未完成的任务点数
    @Autowired
    private PromptFileMapper promptFileMapper;

    public List<GenGenericResp> notFinishTask(String loginName) {
        List<GenGenericResp> respList = new ArrayList<>();
        try {
            int lumenCount = 0;
            //重置notFinishTask点数
            redisService.deleteFieldFromHash(USER_NOT_FINISH_TASK_LUMENS, loginName);
            //获取用户未完成的任务信息
            List<String> userNotFinishTaskList = redisService.getAllKeysFromHash(loginName);
            //如果当前用户没有未完成任务，则直接返回
            if (CollectionUtils.isEmpty(userNotFinishTaskList)) {
                return respList;
            }

            /**
             * 对taskId数据进行相关解析
             */
            HashMap<String, Integer> notFinishTaskMap = new HashMap<>();
            List<String> markIds = new ArrayList<>();
            for (String userNotFinishTask : userNotFinishTaskList) {
                //截取用户的promptId
                Integer notFinishTaskValue = (Integer) redisService.getDataFromHash(loginName, userNotFinishTask);
                markIds.add(userNotFinishTask);
                //为空的值不存入redis
                if (!Objects.isNull(notFinishTaskValue)) {
                    notFinishTaskMap.put(userNotFinishTask, notFinishTaskValue);
                }
            }

            /**
             * 根据taskId去数据库查询相关的数据
             */
            LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<PromptRecord>();
            qw.in(PromptRecord::getMarkId, markIds);
            qw.eq(PromptRecord::getLoginName, loginName);
            qw.eq(PromptRecord::getDel, Boolean.FALSE);
            List<PromptRecord> promptRecordList = promptRecordMapper.selectList(qw);

            /**
             * 对结果集进行标准化处理，返回给前端
             */
            if (!CollectionUtils.isEmpty(promptRecordList)) {
                for (PromptRecord promptRecord : promptRecordList) {
                    GenGenericResp resp = new GenGenericResp();
                    GenGenericPara genGenericPara = JsonUtils.fromJsonNode(promptRecord.getGenInfo(), GenGenericPara.class);
                    //生图或者去背景的批量生图数改为1
                    if (!OriginCreate.create.getValue().equals(promptRecord.getOriginCreate()) &&
                            !OriginCreate.picCreate.getValue().equals(promptRecord.getOriginCreate())) {
                        Resolution resolution = new Resolution();
                        BeanUtils.copyProperties(genGenericPara.getResolution(), resolution);
                        resolution.setBatch_size(1);
                        genGenericPara.setResolution(resolution);
                    }
                    BeanUtils.copyProperties(genGenericPara, resp);
                    resp.setPromptId(promptRecord.getPromptId());
                    resp.setTaskId(promptRecord.getTaskId());
                    resp.setNumber(promptRecord.getTaskNumber());
                    resp.setMarkId(promptRecord.getMarkId());
                    resp.setIndex(notFinishTaskMap.containsKey(promptRecord.getMarkId()) ? notFinishTaskMap.get(promptRecord.getMarkId()) : -1);
                    resp.setCreateTimestamp(promptRecord.getCreateTime());
                    resp.setOriginCreate(promptRecord.getOriginCreate());
                    resp.setIsPublic(PublicType.undisclosed.getValue());
                    resp.setFastHour(promptRecord.getFastHour());
                    resp.setFeatureName(promptRecord.getFeatureName());
                    respList.add(resp);

                    //判断fastHour的lumen点数,且任务在当前一个小时之内
                    if (promptRecord.getFastHour() && (promptRecord.getCreateTime().isAfter(LocalDateTime.now().minusHours(1)))) {
                        //获取模型信息
                        ModelInformation.ModelAbout modelAbout = modelService.getByModelId(promptRecord.getModelId());
                        int taskCostLumen;
                        int batchSizeNum = genGenericPara.getResolution().getBatch_size();
                        //去背景为1点
                        if (Objects.equals(OriginCreate.removeBackground.getValue(), promptRecord.getOriginCreate())) {
                            taskCostLumen = batchSizeNum;
                        } else if (Objects.equals(OriginCreate.hiresFix.getValue(), promptRecord.getOriginCreate())) {
                            //超分按像素计算
                            double scale = promptRecord.getPromptParams().get("scale_by").asDouble();
                            String imgUrl = promptRecord.getPromptParams().get("img_url").asText();
                            PromptFile promptFile = promptFileMapper.selectOne(new LambdaQueryWrapper<>(PromptFile.class)
                                    .eq(PromptFile::getLoginName, loginName)
                                    .eq(PromptFile::getFileUrl, imgUrl)
                                    .last("limit 1"));
                            taskCostLumen = LogicUtil.calculateCostLumenByPixel((int) DoubleMathUtils.mul(promptFile.getWidth(), scale),
                                    (int) DoubleMathUtils.mul(promptFile.getHeight(), scale)) * modelAbout.getCoefficientByPixel();
                        } else {
                            //其余按张数计算
                            if (!Objects.isNull(genGenericPara.getHighPixels()) && genGenericPara.getHighPixels()) {
                                taskCostLumen = 10 * batchSizeNum;
                            } else {
                                taskCostLumen = batchSizeNum * modelAbout.getCoefficientByNum();
                            }
                        }
                        lumenCount += taskCostLumen;
                        resp.setCostLumen(taskCostLumen);
                    }
                }

                //notFinishTask 更新redis中未完成的任务点数
                redisService.incrementFieldInHash(USER_NOT_FINISH_TASK_LUMENS, loginName, lumenCount);
            }
        } catch (Exception e) {
            log.error("获取用户未完成的任务及更新lumen预扣点数异常, loginName: {}", loginName, e);
        }

        return respList;
    }

    public int getUserSurplusLumens(User user) {
        Integer userRechargeTotalLumens = (Integer) redisService.getDataFromHash(USER_RECHARGE_TOTAL_LUMENS, user.getLoginName());
        Integer userRechargeUseLumens = (Integer) redisService.getDataFromHash(USER_RECHARGE_USE_LUMENS, user.getLoginName());
        Integer userVipTotalLumens = (Integer) redisService.getDataFromHash(USER_VIP_TOTAL_LUMENS, user.getLoginName());
        Integer userVipUseLumens = (Integer) redisService.getDataFromHash(USER_VIP_USE_LUMENS, user.getLoginName());
        Integer userGiftTotalLumens = (Integer) redisService.getDataFromHash(USER_GIFT_TOTAL_LUMENS, user.getLoginName());
        Integer userGiftUseLumens = (Integer) redisService.getDataFromHash(USER_GIFT_USE_LUMENS, user.getLoginName());
        //系统奖励点数
        int systemRewardLumen = user.getSystemRewardLumen() == null ? 0 : user.getSystemRewardLumen();

        /**
         * 如果reids中的数据为空，则从数据库中取出
         */
        if (Objects.isNull(userRechargeTotalLumens) || Objects.isNull(userRechargeUseLumens)
                || Objects.isNull(userVipTotalLumens) || Objects.isNull(userVipUseLumens)
                || Objects.isNull(userGiftTotalLumens) || Objects.isNull(userGiftUseLumens)) {
            UserLumens userLumens = getUserLumens(user);

            userRechargeTotalLumens = userLumens.getRechargeLumens();
            userRechargeUseLumens = userLumens.getRechargeLumens() - userLumens.getLeftRechargeLumens();
            userVipTotalLumens = userLumens.getVipLumens();
            userVipUseLumens = userLumens.getVipLumens() - userLumens.getLeftVipLumens();
            userGiftTotalLumens = userLumens.getGiftLumens();
            userGiftUseLumens = userLumens.getGiftLumens() - userLumens.getLeftGiftLumens();
            //已经将系统奖励点数计算到赠送点数中
            systemRewardLumen = 0;
        }

        Integer notFinishTaskLumens = getNotFinishTaskLumen(user.getLoginName());

        Integer dailyLumens = user.getDailyLumens() == null ? 0 : user.getDailyLumens();
        Integer useDailyLumens = user.getUseDailyLumens() == null ? 0 : user.getUseDailyLumens();


        //计算剩余的点数
        int userSurplusLumens = (userRechargeTotalLumens + userVipTotalLumens + userGiftTotalLumens + dailyLumens + systemRewardLumen) -
                (userRechargeUseLumens + userVipUseLumens + userGiftUseLumens + useDailyLumens + notFinishTaskLumens);

        if (userSurplusLumens < 0) {
            log.error("用户：{}，剩余点数为负数，userSurplusLumens: {}", user.getLoginName(), userSurplusLumens);
            userSurplusLumens = 0;
        }
        return userSurplusLumens;
    }

    /**
     * 查询当前用户的点数信息
     *
     * @return
     */
    public UserLumens getUserLumens(User user) {
        try {
            UserLumens userLumens = new UserLumens();
            Integer dailyLumens = user.getDailyLumens() == null ? 10 : user.getDailyLumens();
            Integer userDailyLumens = user.getUseDailyLumens() == null ? 0 : user.getUseDailyLumens();
            Long dailyLumensTime = user.getDailyLumensTime();

            //查询每日免费点数点数情况
            userLumens.setDailyLumens(dailyLumens);
            userLumens.setLeftDailyLumens(dailyLumens - userDailyLumens);
            if (dailyLumensTime != null) {
                LocalDateTime localDateTime = DateUtils.convertUtcToBeijingLocalDateTime(dailyLumensTime);
                userLumens.setDailyLumensTime(localDateTime);
            }
            //系统奖励点数
            int systemRewardLumen = user.getSystemRewardLumen() == null ? 0 : user.getSystemRewardLumen();

            //获取用户的点数情况
            Integer userRechargeTotalLumens = (Integer) redisService.getDataFromHash(USER_RECHARGE_TOTAL_LUMENS, user.getLoginName());
            Integer userRechargeUseLumens = (Integer) redisService.getDataFromHash(USER_RECHARGE_USE_LUMENS, user.getLoginName());
            Integer userVipTotalLumens = (Integer) redisService.getDataFromHash(USER_VIP_TOTAL_LUMENS, user.getLoginName());
            Integer userVipUseLumens = (Integer) redisService.getDataFromHash(USER_VIP_USE_LUMENS, user.getLoginName());
            Integer userGiftTotalLumens = (Integer) redisService.getDataFromHash(USER_GIFT_TOTAL_LUMENS, user.getLoginName());
            if (userGiftTotalLumens != null) {
                //系统奖励点数算到赠送点数中
                userGiftTotalLumens += systemRewardLumen;
            }
            Integer userGiftUseLumens = (Integer) redisService.getDataFromHash(USER_GIFT_USE_LUMENS, user.getLoginName());
            //未完成任务预扣点数
            Integer notFinishTaskLumen = getNotFinishTaskLumen(user.getLoginName());

            //如果有一个点数为空，则查数据库，重新进行统计
            if (Objects.isNull(userRechargeTotalLumens) || Objects.isNull(userRechargeUseLumens)
                    || Objects.isNull(userVipTotalLumens) || Objects.isNull(userVipUseLumens)
                    || Objects.isNull(userGiftTotalLumens) || Objects.isNull(userGiftUseLumens)) {
                Long nowTimeLong = System.currentTimeMillis() / 1000;
                //查询会员有效点数和充值有效点数
                LambdaQueryWrapper<PayLumenRecord> payRecordlqw = new LambdaQueryWrapper();
                payRecordlqw.eq(PayLumenRecord::getLoginName, user.getLoginName());
                payRecordlqw.eq(PayLumenRecord::getInvalid, 0);
                payRecordlqw.le(PayLumenRecord::getCurrentPeriodStart, nowTimeLong);
                payRecordlqw.gt(PayLumenRecord::getCurrentPeriodEnd, nowTimeLong);
                payRecordlqw.orderByDesc(PayLumenRecord::getType);
                payRecordlqw.orderByAsc(PayLumenRecord::getCreateTime);
                List<PayLumenRecord> payLumenRecordList = payLumenRecordMapper.selectList(payRecordlqw);

                Map<Integer, Integer> lumenMap = new HashMap<>();
                Map<Integer, Integer> leftLumenMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(payLumenRecordList)) {
                    // 分组并求lumen的和
                    lumenMap = payLumenRecordList.stream()
                            .collect(Collectors.groupingBy(
                                    PayLumenRecord::getType,                // 按 type 分组
                                    Collectors.summingInt(PayLumenRecord::getLumenQty) // 计算 lumen_qty 的总和
                            ));

                    // 分组并求剩余lumen的和
                    leftLumenMap = payLumenRecordList.stream()
                            .collect(Collectors.groupingBy(
                                    PayLumenRecord::getType,                // 按 type 分组
                                    Collectors.summingInt(PayLumenRecord::getLumenLeftQty) // 计算 lumen_left_qty 的总和
                            ));
                }

                // 获取充值和赠送的点数
                int rechargeLumens = !Objects.isNull(lumenMap.get(LumenType.recharge.getValue())) ? lumenMap.get(LumenType.recharge.getValue()) : 0;
                int giftLumens = !Objects.isNull(lumenMap.get(LumenType.gift.getValue())) ? lumenMap.get(LumenType.gift.getValue()) : 0;
                int vipLumens = !Objects.isNull(lumenMap.get(LumenType.vip.getValue())) ? lumenMap.get(LumenType.vip.getValue()) : 0;

                // 获取充值和赠送的剩余点数
                int leftRechargeLumens = !Objects.isNull(leftLumenMap.get(LumenType.recharge.getValue())) ? leftLumenMap.get(LumenType.recharge.getValue()) : 0;
                int leftGiftLumens = !Objects.isNull(leftLumenMap.get(LumenType.gift.getValue())) ? leftLumenMap.get(LumenType.gift.getValue()) : 0;
                int leftVipLumens = !Objects.isNull(leftLumenMap.get(LumenType.vip.getValue())) ? leftLumenMap.get(LumenType.vip.getValue()) : 0;

                //设置会员和充值标准点数
                userLumens.setRechargeLumens(rechargeLumens);
                userLumens.setVipLumens(vipLumens);
                userLumens.setGiftLumens(giftLumens + systemRewardLumen);
                //设置会员和剩余标准点数
                userLumens.setLeftRechargeLumens(leftRechargeLumens);
                userLumens.setLeftVipLumens(leftVipLumens);
                userLumens.setLeftGiftLumens(leftGiftLumens + systemRewardLumen);
                //设置剩余点数总数
                int leftTotal = userLumens.getLeftDailyLumens() + leftRechargeLumens + leftVipLumens + userLumens.getLeftGiftLumens() - notFinishTaskLumen;
                if (leftTotal < 0) {
                    log.error("用户：{}，DB剩余点数为负数，leftTotal: {}", user.getLoginName(), leftTotal);
                    leftTotal = 0;
                }
                userLumens.setLeftTotalLumens(leftTotal);

                redisService.putDataToHash(USER_RECHARGE_TOTAL_LUMENS, user.getLoginName(), userLumens.getRechargeLumens());
                redisService.putDataToHash(USER_RECHARGE_USE_LUMENS, user.getLoginName(), userLumens.getRechargeLumens() - userLumens.getLeftRechargeLumens());
                redisService.putDataToHash(USER_VIP_TOTAL_LUMENS, user.getLoginName(), userLumens.getVipLumens());
                redisService.putDataToHash(USER_VIP_USE_LUMENS, user.getLoginName(), userLumens.getVipLumens() - userLumens.getLeftVipLumens());
                redisService.putDataToHash(USER_GIFT_TOTAL_LUMENS, user.getLoginName(), giftLumens);
                redisService.putDataToHash(USER_GIFT_USE_LUMENS, user.getLoginName(), giftLumens - leftGiftLumens);

            } else {
                userLumens.setRechargeLumens(userRechargeTotalLumens);
                userLumens.setVipLumens(userVipTotalLumens);
                userLumens.setGiftLumens(userGiftTotalLumens);
                //设置会员和剩余标准点数
                userLumens.setLeftRechargeLumens(userRechargeTotalLumens - userRechargeUseLumens);
                userLumens.setLeftVipLumens(userVipTotalLumens - userVipUseLumens);
                userLumens.setLeftGiftLumens(userGiftTotalLumens - userGiftUseLumens);
                //设置剩余点数总数
                int leftTotal = userLumens.getLeftDailyLumens() + userLumens.getLeftRechargeLumens() + userLumens.getLeftVipLumens() + userLumens.getLeftGiftLumens() - notFinishTaskLumen;
                if (leftTotal < 0) {
                    log.error("用户：{}，缓存剩余点数为负数，leftTotal: {}", user.getLoginName(), leftTotal);
                    leftTotal = 0;
                }
                userLumens.setLeftTotalLumens(leftTotal);
            }

            return userLumens;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Integer getNotFinishTaskLumen(String loginName) {
        Integer notFinishTaskLumen = (Integer) redisService.getDataFromHash(USER_NOT_FINISH_TASK_LUMENS, loginName);
        notFinishTaskLumen = !Objects.isNull(notFinishTaskLumen) ? notFinishTaskLumen : 0;

        Integer notFinishRmbg = (Integer) redisService.getDataFromHash(LogicConstants.BATCH_RMBG_LUMEN_ADVANCE_DEDUCT_KEY, loginName);
        notFinishRmbg = !Objects.isNull(notFinishRmbg) ? notFinishRmbg : 0;
        notFinishTaskLumen += notFinishRmbg;


        return notFinishTaskLumen;
    }

    /**
     * 用户领取系统奖励lumen
     */
    public void receiveSystemRewardLumen(String loginName, Integer rewardLumen) {
        if (StringUtils.isBlank(loginName) || rewardLumen == null || rewardLumen <= 0) {
            log.error("用户领取系统奖励lumen失败, 参数异常, loginName: {}, rewardLumen: {}", loginName, rewardLumen);
            throw new ServerInternalException("The lumen claim failed and the parameters were illegal.");
        }
        RLock lock = redissonClient.getLock(LockPrefixConstant.DEAL_USER_LUMEN_LOCK_PREFIX + loginName);
        try {
            lock.lock();
            User user = userMapper.selectOne(new LambdaQueryWrapper<>(User.class)
                    .eq(User::getLoginName, loginName));
            if (user == null) {
                throw new ServerInternalException("The lumen claim failed and, the user does not exist.");
            }
            Integer userLumen = user.getSystemRewardLumen();
            if (userLumen == null) {
                userLumen = 0;
            }
            user.setSystemRewardLumen(userLumen + rewardLumen);
            user.setUpdateTime(LocalDateTime.now());
            userMapper.updateById(user);
        } catch (Exception e) {
            log.error("用户领取系统奖励lumen异常, loginName: {}, rewardLumen: {}", loginName, rewardLumen, e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
