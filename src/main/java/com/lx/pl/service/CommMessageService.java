package com.lx.pl.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.db.mysql.community.entity.*;
import com.lx.pl.db.mysql.gen.entity.GptPlatformActivity;
import com.lx.pl.db.mysql.gen.entity.GptSysUpdate;
import com.lx.pl.db.mysql.gen.entity.StripeProduct;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.StripeProductMapper;
import com.lx.pl.dto.CommMessageParams;
import com.lx.pl.dto.CommPageInfo;
import com.lx.pl.dto.CommUserMessageNums;
import com.lx.pl.enums.*;
import com.lx.pl.exception.ServerInternalException;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


@Service
@Slf4j
public class CommMessageService {

    public static final String COMM_USER_NOT_READ_MESSAGE = "COMM_USER_NOT_READ_MESSAGE" + ":";

    public static final String COMM_USER_READ_MESSAGE = "COMM_USER_READ_MESSAGE" + ":";


    /**
     * web系统更新总数
     */
    public static final String WEB_SYSUPDATE_NUMS = "WEB_SYSUPDATE_NUMS";

    /**
     * ios系统更新总数
     */
    public static final String IOS_SYSUPDATE_NUMS = "IOS_SYSUPDATE_NUMS";

    /**
     * 安卓系统更新总数
     */
    public static final String ANDROID_SYSUPDATE_NUMS = "ANDROID_SYSUPDATE_NUMS";

    /**
     * 会员系统活动公告总数，包含给所有用户和vip用户的公告活动
     */
    public static final String VIP_PLATFORM_ACTIVITY_NUMS = "VIP_PLATFORM_ACTIVITY_NUMS";

    /**
     * 非会员系统活动公告总数，包含给所有用户和非vip用户的公告活动
     */
    public static final String NOT_VIP_PLATFORM_ACTIVITY_NUMS = "NOT_VIP_PLATFORM_ACTIVITY_NUMS";


    public static final String NOT_READ_LIKE_NUMS = "NOT_READ_LIKE_NUMS";

    public static final String NOT_READ_COMMENT_NUMS = "NOT_READ_COMMENT_NUMS";

    public static final String NOT_READ_PLATFORM_MESSAGE_NUMS = "NOT_READ_PLATFORM_MESSAGE_NUMS";

    /**
     * web平台专属用户未读消息数
     */
    public static final String WEB_NOT_READ_PLATFORM_MESSAGE_NUMS = "WEB_NOT_READ_PLATFORM_MESSAGE_NUMS";

    /**
     * ios平台专属用户未读消息数
     */
    public static final String IOS_NOT_READ_PLATFORM_MESSAGE_NUMS = "IOS_NOT_READ_PLATFORM_MESSAGE_NUMS";

    /**
     * android平台专属用户未读消息数
     */
    public static final String ANDROID_NOT_READ_PLATFORM_MESSAGE_NUMS = "ANDROID_NOT_READ_PLATFORM_MESSAGE_NUMS";

    /**
     * 系统更新用户已读数
     */
    public static final String WEB_READ_SYSUPDATE_NUMS = "WEB_READ_SYSUPDATE_NUMS";

    public static final String IOS_READ_SYSUPDATE_NUMS = "IOS_READ_SYSUPDATE_NUMS";

    public static final String ANDROID_READ_SYSUPDATE_NUMS = "ANDROID_READ_SYSUPDATE_NUMS";

    /**
     * 已读会员系统活动公告，包含给所有用户和vip用户的公告活动
     */
    public static final String READ_VIP_PLATFORM_ACTIVITY_NUMS = "READ_VIP_PLATFORM_ACTIVITY_NUMS";

    /**
     * 已读非会员系统活动公告，包含给所有用户和非vip用户的公告活动
     */
    public static final String READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS = "READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS";

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private RedisService redisService;

    @Lazy
    @Autowired
    private CommLikeService commLikeService;

    @Lazy
    @Autowired
    private CommCommentService commCommentService;

    @Autowired
    private PlatformActivityService platformActivityService;

    @Autowired
    private PlatformMessageService platformMessageService;

    @Autowired
    private SysUpdateService sysUpdateService;

    @Autowired
    @Lazy
    private UserService userService;

    @Resource
    private StripeProductMapper stripeProductMapper;
    @Autowired
    private LumenService lumenService;


    public CommUserMessageNums getCommUserMessageNums(User user, String platform) {
        CommUserMessageNums commUserMessageNums = new CommUserMessageNums();
        String loginName = user.getLoginName();

        try {
            //获取用户未读消息
            Map<String, Object> notReadResultMap = redisService.getHashAsMap(COMM_USER_NOT_READ_MESSAGE + loginName);
            int nLikeNums = (int) Optional.ofNullable(notReadResultMap.get(NOT_READ_LIKE_NUMS)).orElse(0);
            int nCommentNums = (int) Optional.ofNullable(notReadResultMap.get(NOT_READ_COMMENT_NUMS)).orElse(0);
            int nPlatformMessageNums = (int) Optional.ofNullable(notReadResultMap.get(NOT_READ_PLATFORM_MESSAGE_NUMS)).orElse(0);
            int exclusivePlatformMessageNums = getExclusivePlatformMessageNums(platform, notReadResultMap);
            nPlatformMessageNums += exclusivePlatformMessageNums;


            //获取用户已读消息
            Map<String, Object> readResultMap = redisService.getHashAsMap(COMM_USER_READ_MESSAGE + loginName);
            if (Objects.isNull(readResultMap) || readResultMap.isEmpty() ){
                readResultMap = getReadResultMap(user);
            }
            int readSysUpdateNums = getReadSysUpdateNums(platform, readResultMap);
            //包含给所有用户和vip用户的公告活动
            int readVipPlatformActivityNums = (int) Optional.ofNullable(readResultMap.get(READ_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
            //包含给所有用户和非vip用户的公告活动
            int readNotVipPlatformActivityNums = (int) Optional.ofNullable(readResultMap.get(READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);


            //获取平台活动和系统更新总数
            //系统更新总数
            int sysupdateNums = getSysupdateNums(platform);
            //平台活动vip用户活动总数
            int vipPlatformActivityNums = (int) Optional.ofNullable(redisService.get(VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
            //平台活动非vip用户活动总数
            int notVipPlatformActivityNums = (int) Optional.ofNullable(redisService.get(NOT_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);

            // 获取社区活动公共数量
            Set<Long> commActivityIds = redisService.getDataFromSet(LogicConstants.COMM_ACTIVITY_PUBLIC);

            // 获取用户已查看活动数量
            int userCommActivityNum = (int) Optional.ofNullable(redisService.getDataFromHash(LogicConstants.COMM_ACTIVITY_USER_NUM, loginName)).orElse(0);

            // 用户未读社区活动信息数量
            Integer commActivityNums = 0;
            if (CollectionUtils.isNotEmpty(commActivityIds) && userCommActivityNum <= commActivityIds.size()) {
                // do something
                commActivityNums = commActivityIds.size() - userCommActivityNum;
            }

            //(系统更新和平台活动)未读数 = 总数 - 已读数
            int nPlatformActivityNums = 0;
            if (VipType.basic.getValue().equals(user.getVipType())) {
                nPlatformActivityNums = notVipPlatformActivityNums - readNotVipPlatformActivityNums;
            } else {
                nPlatformActivityNums = vipPlatformActivityNums - readVipPlatformActivityNums;
            }
            //未读系统更新总数
            int nSysUpdateNums = sysupdateNums - readSysUpdateNums;

            /**
             * 避免脏数据出现负值
             */
            nLikeNums = nLikeNums >= 0 ? nLikeNums : 0;
            nCommentNums = nCommentNums >= 0 ? nCommentNums : 0;
            nSysUpdateNums = nSysUpdateNums >= 0 ? nSysUpdateNums : 0;
            nPlatformMessageNums = nPlatformMessageNums >= 0 ? nPlatformMessageNums : 0;
            nPlatformActivityNums = nPlatformActivityNums >= 0 ? nPlatformActivityNums : 0;

            //将未读信息值返回
            commUserMessageNums.setCommActivityNums(commActivityNums);
            commUserMessageNums.setNLikeNums(nLikeNums);
            commUserMessageNums.setNCommentNums(nCommentNums);
            commUserMessageNums.setNSysUpdateNums(nSysUpdateNums);
            commUserMessageNums.setNPlatformMessageNums(nPlatformMessageNums);
            commUserMessageNums.setNPlatformActivityNums(nPlatformActivityNums);
            commUserMessageNums.setNMessageTotalNums(nLikeNums + nCommentNums + nSysUpdateNums + nPlatformMessageNums + nPlatformActivityNums);
            // 用户当前可用lumen点数
            commUserMessageNums.setCurrentLumen(lumenService.getUserSurplusLumens(user));
        } catch (Exception e) {
            log.error("用户：{},查询未读消息 失败", loginName, e);
            throw new ServerInternalException("get not read Message Failed");
        }
        return commUserMessageNums;
    }

    private int getExclusivePlatformMessageNums(String platform, Map<String, Object> notReadResultMap) {
        int exclusivePlatformMessageNums = 0;
        switch (platform) {
            case "web":
                exclusivePlatformMessageNums = (int) Optional.ofNullable(notReadResultMap.get(WEB_NOT_READ_PLATFORM_MESSAGE_NUMS)).orElse(0);
                break;
            case "ios":
                exclusivePlatformMessageNums = (int) Optional.ofNullable(notReadResultMap.get(IOS_NOT_READ_PLATFORM_MESSAGE_NUMS)).orElse(0);
                break;
            case "android":
                exclusivePlatformMessageNums = (int) Optional.ofNullable(notReadResultMap.get(ANDROID_NOT_READ_PLATFORM_MESSAGE_NUMS)).orElse(0);
                break;
        }
        return exclusivePlatformMessageNums;
    }

    public Boolean reduceCommUserMessageNums(CommMessageParams commMessageParams, User user, String platform) {

        //判断是否是点赞还是评论还是系统更新还是平台信息还是平台活动
        if (!Objects.isNull(commMessageParams.getMessageType())) {
            switch (MessageType.fromValue(commMessageParams.getMessageType())) {
                case LIKE:
                    dealLike(user, commMessageParams);
                    break;
                case COMMENT:
                    dealComment(user, commMessageParams);
                    break;
                case SYS_UPDATE:
                    dealSysUpdate(user, commMessageParams, platform);
                    break;
                case PLATFORM_MESSAGE:
                    dealPlatformMessage(user, commMessageParams);
                    break;
                case PLATFORM_ACTIVITY:
                    dealPlatformActivity(user, commMessageParams);
                    break;
            }
        }

        return Boolean.TRUE;
    }

    //处理点赞已读
    public void dealLike(User user, CommMessageParams commMessageParams) {
        decrementFieldSafely(COMM_USER_NOT_READ_MESSAGE + user.getLoginName(), NOT_READ_LIKE_NUMS);
        commLikeService.readCommLike(commMessageParams.getMessageId(), user);
    }

    //处理评论已读
    public void dealComment(User user, CommMessageParams commMessageParams) {
        decrementFieldSafely(COMM_USER_NOT_READ_MESSAGE + user.getLoginName(), NOT_READ_COMMENT_NUMS);
        commCommentService.readCommComment(commMessageParams.getMessageId(), user);
    }

    //处理系统消息已读
    public void dealSysUpdate(User user, CommMessageParams commMessageParams, String platform) {
        switch (platform) {
            case "web":
                redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), WEB_READ_SYSUPDATE_NUMS, 1);
                break;
            case "ios":
                redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), IOS_READ_SYSUPDATE_NUMS, 1);
                break;
            case "android":
                redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), ANDROID_READ_SYSUPDATE_NUMS, 1);
                break;
        }
        sysUpdateService.readSysUpdate(commMessageParams.getMessageId(), user, platform);
    }

    //处理平台消息已读
    public void dealPlatformMessage(User user, CommMessageParams commMessageParams) {
        //查询消息归属平台
        if (StringUtils.isBlank(commMessageParams.getMessageId())) {
            return;
        }
        UserPlatformMessage platformMessage = mongoTemplate.findOne(
                new Query().addCriteria(Criteria.where("id").is(commMessageParams.getMessageId())), UserPlatformMessage.class);
        if (platformMessage == null) {
            return;
        }

        decrementPlatformMessage(platformMessage.getPlatform(), user.getLoginName());
        platformMessageService.readPlatformMessage(commMessageParams.getMessageId(), user);
    }

    private void decrementPlatformMessage(List<String> platform, String loginName) {

        // 判断是否是全平台（是否包含所有平台）
        if (CollectionUtils.isEmpty(platform) || new HashSet<>(platform).containsAll(ClientType.allValues)) {
            // 全平台共有
            redisService.decrementHash(COMM_USER_NOT_READ_MESSAGE + loginName, NOT_READ_PLATFORM_MESSAGE_NUMS);
        } else {
            if (platform.contains(ClientType.web.getValue())) {
                redisService.decrementHash(COMM_USER_NOT_READ_MESSAGE + loginName, WEB_NOT_READ_PLATFORM_MESSAGE_NUMS);
            }

            if (platform.contains(ClientType.ios.getValue())) {
                redisService.decrementHash(COMM_USER_NOT_READ_MESSAGE + loginName, IOS_NOT_READ_PLATFORM_MESSAGE_NUMS);
            }

            if (platform.contains(ClientType.android.getValue())) {
                redisService.decrementHash(COMM_USER_NOT_READ_MESSAGE + loginName, ANDROID_NOT_READ_PLATFORM_MESSAGE_NUMS);
            }
        }
    }

    //处理平台活动已读
    public void dealPlatformActivity(User user, CommMessageParams commMessageParams) {

        GptPlatformActivity one = platformActivityService.getPlatformActivityById(commMessageParams.getMessageId());
        if (one.getUserType().equals(PlatformUserTypeEnum.all.getValue())){
            // 公共消息 则此用户的非会员 已读和非会员已读都+1 (兼容后面用户 改vip 状态 数据不一致)
            redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1);
            redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), READ_VIP_PLATFORM_ACTIVITY_NUMS, 1);

        }else {
            if (VipType.basic.getValue().equals(user.getVipType())) {
                redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS, 1);
            } else {
                redisService.incrementFieldInHash(COMM_USER_READ_MESSAGE + user.getLoginName(), READ_VIP_PLATFORM_ACTIVITY_NUMS, 1);
            }
        }
        platformActivityService.readPlatformActivity(commMessageParams.getMessageId(), user);
    }

    public Boolean reduceReadAll(User user, String platform) {
        String loginName = user.getLoginName();
        String notReadNumsKey = COMM_USER_NOT_READ_MESSAGE + loginName;

        //采用延时双删策略
        redisService.unlink(notReadNumsKey);
        dealReadMessage(user, platform);

        //处理点赞已读的历史数据
        commLikeService.readCommLike("", user);
        //处理评论已经得历史数据
        commCommentService.readCommComment("", user);
        //处理系统消息已经得历史数据
        sysUpdateService.readSysUpdate("", user, platform);
        //处理平台消息已经得历史数据
        platformMessageService.readPlatformMessage("", user);
        //处理平台活动已经得历史数据
        platformActivityService.readPlatformActivity("", user);

        redisService.unlink(notReadNumsKey);
        dealReadMessage(user, platform);

        return Boolean.TRUE;
    }

    public void decrementFieldSafely(String key, String field) {
        Integer currentValue = (Integer) redisService.getDataFromHash(key, field);
        if (currentValue != null && currentValue > 0) {
            redisService.incrementFieldInHash(key, field, -1);
        }
    }

    public void dealReadMessage(User user, String platform) {
        //获取平台活动和系统更新总数
        //系统更新总数
        int sysupdateNums = getSysupdateNums(platform);
        //平台活动vip用户活动总数
        int vipPlatformActivityNums = (int) Optional.ofNullable(redisService.get(VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);
        //平台活动非vip用户活动总数
        int notVipPlatformActivityNums = (int) Optional.ofNullable(redisService.get(NOT_VIP_PLATFORM_ACTIVITY_NUMS)).orElse(0);

        //将总数更新到已读信息数
        Map<String, Integer> readMessageCounts = new HashMap<>();
        readMessageCounts.put(getSysUpdateNumRedisKey(platform), sysupdateNums);
        readMessageCounts.put(READ_VIP_PLATFORM_ACTIVITY_NUMS, vipPlatformActivityNums);
        readMessageCounts.put(READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS, notVipPlatformActivityNums);

        redisService.putAllToHash(COMM_USER_READ_MESSAGE + user.getLoginName(), readMessageCounts);

    }


    public CommPageInfo<CommLike> getLikeMessage(String lastLikeId, Integer pageSize, User user) {
        return commLikeService.getLikeMessage(lastLikeId, pageSize, user);
    }

    public CommPageInfo<CommComment> getCommentMessage(String lastCommentId, Integer pageSize, User user) {
        return commCommentService.getCommentMessage(lastCommentId, pageSize, user);
    }

    public CommPageInfo<GptSysUpdate> getSysUpdateMessage(String lastSysUpdateId, Integer pageSize, User user, String platform) {
        return sysUpdateService.getSysUpdateMessage(lastSysUpdateId, pageSize, user, platform);
    }

    public CommPageInfo<GptPlatformActivity> getActivityMessage(String lastActivityId, Integer pageSize, User user) {
        return platformActivityService.getActivityMessage(lastActivityId, pageSize, user);
    }

    public CommPageInfo<UserPlatformMessage> getPlatformMessage(String lastPlatformMessId, Integer pageSize, User user, String platform) {
        return platformMessageService.getPlatformMessage(lastPlatformMessId, pageSize, user, platform);
    }

    public Integer getReadSysUpdateNums(String platform, Map<String, Object> readResultMap) {
        Integer readSysUpdateNums = 0;
        switch (platform) {
            case "web":
                readSysUpdateNums = (int) Optional.ofNullable(readResultMap.get(WEB_READ_SYSUPDATE_NUMS)).orElse(0);
                break;
            case "ios":
                readSysUpdateNums = (int) Optional.ofNullable(readResultMap.get(IOS_READ_SYSUPDATE_NUMS)).orElse(0);
                break;
            case "android":
                readSysUpdateNums = (int) Optional.ofNullable(readResultMap.get(ANDROID_READ_SYSUPDATE_NUMS)).orElse(0);
                break;
        }
        return readSysUpdateNums;
    }

    public Integer getSysupdateNums(String platform) {
        Integer sysupdateNums = 0;
        switch (platform) {
            case "web":
                sysupdateNums = (int) Optional.ofNullable(redisService.get(WEB_SYSUPDATE_NUMS)).orElse(0);
                break;
            case "ios":
                sysupdateNums = (int) Optional.ofNullable(redisService.get(IOS_SYSUPDATE_NUMS)).orElse(0);
                break;
            case "android":
                sysupdateNums = (int) Optional.ofNullable(redisService.get(ANDROID_SYSUPDATE_NUMS)).orElse(0);
                break;
        }
        return sysupdateNums;
    }

    public String getSysUpdateNumRedisKey(String platform) {

        // 默认为web
        String sysUpdateNumRedisKey = WEB_READ_SYSUPDATE_NUMS;
        switch (platform) {
            case "web":
                sysUpdateNumRedisKey = WEB_READ_SYSUPDATE_NUMS;
                break;
            case "ios":
                sysUpdateNumRedisKey = IOS_READ_SYSUPDATE_NUMS;
                break;
            case "android":
                sysUpdateNumRedisKey = ANDROID_READ_SYSUPDATE_NUMS;
                break;
        }
        return sysUpdateNumRedisKey;
    }

    public void addUserUpgradeVipMessage(String planLevel, Long vipBeginTime, String priceInterval, Long userId) {

        try {
            UserPlatformMessage userPlatformMessage = new UserPlatformMessage();
            User user = userService.getUserById(userId);

            String loginName = user.getLoginName();
            AccountInfo accountInfo = new AccountInfo();
            accountInfo.setUserId(user.getId());
            accountInfo.setUserLoginName(loginName);
            accountInfo.setUserName(user.getUserName());
            accountInfo.setUserAvatarUrl(user.getAvatarUrl());
            UserPlatformMessType upgrade = UserPlatformMessType.upgrade;
            userPlatformMessage.setAccountInfo(accountInfo);

            String title = String.format("Your Subscription Has Been Updated!");
            String introduction = String.format("Your membership change is complete! Click to see details.");
            userPlatformMessage.setTitle(title);
            userPlatformMessage.setIntroduction(introduction);
            userPlatformMessage.setMessType(upgrade.getValue());
            userPlatformMessage.setPlanLevel(planLevel);
            userPlatformMessage.setPriceInterval(priceInterval);
            userPlatformMessage.setVipEndTime(vipBeginTime);
            userPlatformMessage.setPlatform(upgrade.getPlatformInfo());

            // 插入平台信息
            platformMessageService.addPlatformMessage(userPlatformMessage);

            // 刷新redis
            dealRedisCache(upgrade, user.getLoginName());
        } catch (Exception e) {
            log.error("插入 会员变更站内信错误:{}", e);
        }

    }

    public void addUserVipPayFailMessage(String priceId, Long userId) {

        // 付费失败站内信
        try {
            UserPlatformMessage userPlatformMessage = new UserPlatformMessage();
            User user = userService.getUserById(userId);
            // 暂时只有 strip 有 扣款失败信息
            LambdaQueryWrapper<StripeProduct> sqw = new LambdaQueryWrapper<>();
            sqw.eq(StripeProduct::getStripePriceId, priceId);
            StripeProduct stripeProduct = stripeProductMapper.selectOne(sqw);


            String loginName = user.getLoginName();
            AccountInfo accountInfo = new AccountInfo();
            accountInfo.setUserId(user.getId());
            accountInfo.setUserLoginName(loginName);
            accountInfo.setUserName(user.getUserName());
            accountInfo.setUserAvatarUrl(user.getAvatarUrl());

            UserPlatformMessType failedToDeduct = UserPlatformMessType.FAILED_TO_DEDUCT;
            userPlatformMessage.setAccountInfo(accountInfo);

            String title = String.format("Payment Failed – Membership Paused");
            String introduction = String.format("We couldn’t process your payment. Your membership has been paused. Update payment method to restore access....");
            userPlatformMessage.setTitle(title);
            userPlatformMessage.setIntroduction(introduction);
            userPlatformMessage.setMessType(failedToDeduct.getValue());
            userPlatformMessage.setPlanLevel(stripeProduct.getPlanLevel());
            userPlatformMessage.setPriceInterval(user.getVipType());
            userPlatformMessage.setPlatform(failedToDeduct.getPlatformInfo());


            // 插入平台信息
            platformMessageService.addPlatformMessage(userPlatformMessage);

            // 刷新redis
            dealRedisCache(failedToDeduct, user.getLoginName());
        } catch (Exception e) {
            log.error("插入 扣款失败站内信错误:{}", e);
        }

    }

    public void dealRedisCache(UserPlatformMessType messageType, String loginName) {
        if (UserPlatformMessType.allPlatform(messageType)) {
            redisService.incrementFieldInHash(COMM_USER_NOT_READ_MESSAGE + loginName, NOT_READ_PLATFORM_MESSAGE_NUMS, 1);
        } else {
            if (messageType.getPlatformInfo().contains(ClientType.web.getValue())) {
                redisService.incrementFieldInHash(COMM_USER_NOT_READ_MESSAGE + loginName, WEB_NOT_READ_PLATFORM_MESSAGE_NUMS, 1);
            }

            if (messageType.getPlatformInfo().contains(ClientType.ios.getValue())) {
                redisService.incrementFieldInHash(COMM_USER_NOT_READ_MESSAGE + loginName, IOS_NOT_READ_PLATFORM_MESSAGE_NUMS, 1);
            }

            if (messageType.getPlatformInfo().contains(ClientType.android.getValue())) {
                redisService.incrementFieldInHash(COMM_USER_NOT_READ_MESSAGE + loginName, ANDROID_NOT_READ_PLATFORM_MESSAGE_NUMS, 1);
            }
        }
    }

    public Map<String, Object> getReadResultMap(User user){

        Map<String, Object>  resultMap = new HashMap<>();

        // 组装系统消息
        Integer webSysUpdate = 0;
        Integer iosSysUpdate = 0;
        Integer androidSysUpdate = 0;
        List<UserSysUpdate> allReadSysUpdateByUser = sysUpdateService.getAllReadSysUpdateByUser(user);
        if (!CollectionUtils.isEmpty(allReadSysUpdateByUser)){
            for(UserSysUpdate userSysUpdate : allReadSysUpdateByUser){
                switch (userSysUpdate.getPlatform()){
                    case "web":
                        webSysUpdate++;
                        break;
                    case "ios":
                        iosSysUpdate++;
                        break;
                    case "android":
                        androidSysUpdate++;
                        break;
                }
            }
        }
        resultMap.put(WEB_READ_SYSUPDATE_NUMS,webSysUpdate);
        resultMap.put(IOS_READ_SYSUPDATE_NUMS,iosSysUpdate);
        resultMap.put(ANDROID_READ_SYSUPDATE_NUMS,androidSysUpdate);

        // 组装平台消息
        List<GptPlatformActivity> allActivityMessageByUser = platformActivityService.getAllActivityMessageByUser(user);
        Integer vipPlatformActivity = 0;
        Integer notVipPlatformActivity = 0;
        if (!CollectionUtils.isEmpty(allActivityMessageByUser)){
            for(GptPlatformActivity gptPlatformActivity : allActivityMessageByUser){

                // 已读才处理
                if(gptPlatformActivity.getRead()){
                    if (PlatformUserTypeEnum.all.getValue().equals(gptPlatformActivity.getUserType())){
                        vipPlatformActivity++;
                        notVipPlatformActivity++;
                    }else{
                        if (PlatformUserTypeEnum.vip.getValue().equals(gptPlatformActivity.getUserType())){
                            vipPlatformActivity++;
                        }else if (PlatformUserTypeEnum.not_vip.getValue().equals(gptPlatformActivity.getUserType())){
                            notVipPlatformActivity++;
                        }
                    }
                }

            }
        }
        resultMap.put(READ_VIP_PLATFORM_ACTIVITY_NUMS,vipPlatformActivity);
        resultMap.put(READ_NOT_VIP_PLATFORM_ACTIVITY_NUMS,notVipPlatformActivity);
        redisService.putAllToHash(COMM_USER_READ_MESSAGE + user.getLoginName(), resultMap);
        return resultMap;
    }

}
