package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.lx.pl.constant.LogicConstants;
import com.lx.pl.controller.GenController;
import com.lx.pl.db.mysql.gen.entity.*;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.db.mysql.gen.repository.UserTaskQueueRepository;
import com.lx.pl.dto.DealImgParams;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.TaskProcessMessage;
import com.lx.pl.dto.TaskQueueParams;
import com.lx.pl.dto.generic.R;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.PromptStatus;
import com.lx.pl.exception.LogicException;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.util.FileUtils;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.lx.pl.constant.LockPrefixConstant.PRELOADING_LOCK_PREFIX;
import static com.lx.pl.constant.LogicConstants.*;
import static com.lx.pl.service.LoadBalanceService.MARK_ID_QUEUE_NAME;
import static com.lx.pl.service.LoadBalanceService.USER_TASK_TIMESTAMP;

@Service
@Slf4j
public class TaskService {

    @Autowired
    RedisService redisService;

    @Resource
    PromptRecordMapper promptRecordMapper;

    @Resource
    PromptFileMapper promptFileMapper;

    @Autowired
    LoadBalanceService loadBalanceService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private UserTaskQueueRepository userTaskQueueRepository;

    @Autowired
    private GenController genController;

    @Autowired
    private GenService genService;

    @Autowired
    private MidjourneyService midjourneyService;
    @Autowired
    private LumenService lumenService;


    public Boolean cancelTask(String markId, User user) {
        if (StringUtil.isBlank(markId)) {
            return Boolean.FALSE;
        }

        try {
            log.info("用户：{}，删除对应的的任务，markId:{}", user.getLoginName(), markId);
            //移除未完成任务的状态
            Long delResult = redisService.deleteFieldFromHash(user.getLoginName(), markId);
            //如果标记没有删除成功，则任务取消失败
            if (delResult == 0) {
                return Boolean.FALSE;
            }
            //删除排队任务信息
            String queueKey = (String) redisService.getDataFromHash(MARK_ID_QUEUE_NAME, markId);
            if (StringUtil.isNotBlank(queueKey)) {
                if (queueKey.startsWith(WAIT_QUEUE_NAME_PREFIX)) {
                    //移除markId对应的relax延时等待队列
                    redisService.removeTaskFromDelayQueue(queueKey, markId);
                } else {
                    redisService.listRemoveValue(queueKey, markId);
                }
                //移除markId对应的队列名称
                redisService.deleteFieldFromHash(MARK_ID_QUEUE_NAME, markId);
            }
            String serverId = (String) redisService.getDataFromHash(LogicConstants.MARKID_SERVERID_LIST, markId);
            if (StringUtil.isNotBlank(serverId)) {
                if (redisService.listRemoveValue(serverId, markId) > 0) {
                    redisService.comfySizeDecr(LogicConstants.COMFY_SIZE_PREFIX + serverId);
                }
            }
            //删除不用的时间段和时间戳
            try {
                redisService.delete(USER_TASK_TIMESTAMP + markId);
            } catch (Exception e) {
                log.error("删除不用的时间段和时间戳", e);
            }

            //修改任务状态为失败
            LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<PromptRecord>();
            qw.eq(PromptRecord::getMarkId, markId);
            qw.eq(PromptRecord::getLoginName, user.getLoginName());
            qw.eq(PromptRecord::getDel, Boolean.FALSE);
            PromptRecord originPromptRecord = promptRecordMapper.selectOne(qw);
            if (!Objects.isNull(originPromptRecord)) {
                LambdaUpdateWrapper<PromptRecord> luw = new LambdaUpdateWrapper<PromptRecord>();
                luw.eq(PromptRecord::getLoginName, user.getLoginName());
                luw.eq(PromptRecord::getId, originPromptRecord.getId());
                luw.set(PromptRecord::getDel, Boolean.TRUE);
                luw.set(PromptRecord::getFailureMessage, PromptStatus.cancel.getValue());
                luw.set(PromptRecord::getUpdateTime, LocalDateTime.now());
                luw.set(PromptRecord::getUpdateBy, user.getLoginName());
                promptRecordMapper.update(null, luw);
            }

            //立即释放预扣点数
            lumenService.notFinishTask(user.getLoginName());
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("cancelTask error", e);
        }

        return Boolean.FALSE;
    }

    public TaskProcessMessage processTask(String taskId) throws JsonProcessingException {
        // 检查是否为MJ任务
        if (isMidjourneyTask(taskId)) {
            return processMidjourneyTask(taskId);
        }

        // 处理传统markId任务
        return processTraditionalTask(taskId);
    }

    /**
     * 处理传统markId任务
     */
    private TaskProcessMessage processTraditionalTask(String markId) throws JsonProcessingException {
        TaskProcessMessage taskProcessMessage = new TaskProcessMessage();
        String loginName = redisService.stringGet(markId);
        Integer index = (Integer) redisService.getDataFromHash(loginName, markId);
        //任务的结果已经返回
        if (Objects.isNull(index) || StringUtil.isBlank(loginName)) {
            return dealSuccessOrFailureTask(markId, loginName);
        }

        taskProcessMessage.setIndex(index);
        taskProcessMessage.setMarkId(markId);
        //更提交任务
        if (index == -1) {
            taskProcessMessage.setStatus(PromptStatus.newbuilt.getValue());
        } else if (index > 0) {
            //任务进入排队队列
            taskProcessMessage.setStatus(PromptStatus.pending.getValue());
        } else if (index == 0) {
            try {
                //超时任务直接返回
                TaskProcessMessage timeOutTaskMessage = dealTimeOutTask(markId, loginName);
                if (!Objects.isNull(timeOutTaskMessage)) {
                    return timeOutTaskMessage;
                }
                taskProcessMessage.setStatus(PromptStatus.running.getValue());
            } catch (Exception e) {
                log.error("轮询接口，图片正在生图失败，失败markId为:{}", markId, e);
            }
        }

        return taskProcessMessage;
    }

    private TaskProcessMessage dealTimeOutTask(String markId, String loginName) throws JsonProcessingException {
        TaskProcessMessage taskProcessMessage = new TaskProcessMessage();
        //任务开始执行
        Long userFinishTaskTimes = System.currentTimeMillis();
        Long userTaskTimes = (Long) redisService.get(USER_TASK_TIMESTAMP + markId);

        Boolean flagTimeOut = Boolean.FALSE;
        if (!Objects.isNull(userTaskTimes) && (userFinishTaskTimes >= userTaskTimes)) {
            //进入confyui的图片是否超过了5分钟，未返回
            Boolean beMoreTime = ((userFinishTaskTimes - userTaskTimes) > 5 * 60 * 1000) ? Boolean.TRUE : Boolean.FALSE;
            if (beMoreTime) {
                flagTimeOut = Boolean.TRUE;
            }
        }

        if (flagTimeOut) {
            taskProcessMessage.setMarkId(markId);
            taskProcessMessage.setInfo("Task timeout");
            taskProcessMessage.setStatus(PromptStatus.failure.getValue());
            //删除任务标记
            redisService.delete(markId);
            //删除对应的任务
            loadBalanceService.dealFailureTask(markId, "", loginName, "Task timeout");
            //删除不用的时间段和时间戳
            try {
                redisService.delete(USER_TASK_TIMESTAMP + markId);
            } catch (Exception e) {
                log.error("dealTimeOutTask 删除不用的时间段和时间戳", e);
            }
            return taskProcessMessage;
        }

        return null;
    }

    private TaskProcessMessage dealSuccessOrFailureTask(String markId, String loginName) {
        TaskProcessMessage taskProcessMessage = new TaskProcessMessage();
        try {
            LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<PromptRecord>();
            qw.eq(PromptRecord::getMarkId, markId);
            if (StringUtil.isNotBlank(loginName)) {
                qw.eq(PromptRecord::getLoginName, loginName);
            }
            PromptRecord originPromptRecord = promptRecordMapper.selectOne(qw);

            if (!Objects.isNull(originPromptRecord)) {

                //如果返回了失败信息，则直接返回
                if (StringUtil.isNotBlank(originPromptRecord.getFailureMessage())) {
                    taskProcessMessage.setMarkId(markId);
                    if (PromptStatus.cancel.getValue().equals(originPromptRecord.getFailureMessage())) {
                        taskProcessMessage.setStatus(PromptStatus.cancel.getValue());
                    } else {
                        taskProcessMessage.setStatus(PromptStatus.failure.getValue());
                    }
                    // 3001###no face
                    String[] split = originPromptRecord.getFailureMessage().split(FAIL_MSG_SPLIT);
                    if (split.length > 1) {
                        taskProcessMessage.setInfo(split[0]);
                    }
                    //删除任务标记
                    redisService.delete(markId);
                    //删除对应的任务
                    return taskProcessMessage;
                }

                LambdaQueryWrapper<PromptFile> lqwf = new LambdaQueryWrapper<PromptFile>();
                lqwf.eq(PromptFile::getPromptId, originPromptRecord.getPromptId());
                lqwf.eq(PromptFile::getLoginName, originPromptRecord.getLoginName());
                lqwf.eq(PromptFile::getDel, Boolean.FALSE);
                List<PromptFile> promptRecords = promptFileMapper.selectList(lqwf);

                //查询对应的图片信息
                if (!CollectionUtils.isEmpty(promptRecords) && originPromptRecord.getBatchSize() == promptRecords.size()) {
                    List<TaskProcessMessage.ImgUrl> imgUrlList = new ArrayList<>();
                    for (PromptFile promptFile : promptRecords) {
                        TaskProcessMessage.ImgUrl imgUrl = new TaskProcessMessage.ImgUrl();
                        imgUrl.setImgUrl(promptFile.getFileUrl());
                        imgUrl.setThumbnailUrl(promptFile.getThumbnailUrl());
                        imgUrl.setHighThumbnailUrl(promptFile.getHighThumbnailUrl());
                        imgUrl.setMiniThumbnailUrl(promptFile.getMiniThumbnailUrl());
                        imgUrl.setHighMiniUrl(promptFile.getHighMiniUrl());
                        imgUrl.setRealHeight(promptFile.getHeight());
                        imgUrl.setRealWidth(promptFile.getWidth());
                        imgUrl.setSensitive(promptFile.getSensitiveMessage());
                        imgUrl.setImgName(promptFile.getFileName());
                        imgUrlList.add(imgUrl);
                    }

                    taskProcessMessage.setStatus(PromptStatus.success.getValue());
                    taskProcessMessage.setPromptId(originPromptRecord.getPromptId());
                    taskProcessMessage.setImg_urls(imgUrlList);
                    taskProcessMessage.setPrompt(originPromptRecord.getPrompt());
                    taskProcessMessage.setFeatureName(originPromptRecord.getFeatureName());

                    //删除任务标记
                    redisService.delete(markId);
                } else {
                    //超时任务直接返回
                    TaskProcessMessage timeOutTaskMessage = dealTimeOutTask(markId, loginName);
                    if (!Objects.isNull(timeOutTaskMessage)) {
                        return timeOutTaskMessage;
                    }
                    //如果数据还没有入库，则算为排队
                    taskProcessMessage.setIndex(0);
                    taskProcessMessage.setStatus(PromptStatus.running.getValue());
                }
            } else {
                taskProcessMessage.setStatus(PromptStatus.failure.getValue());
            }

            taskProcessMessage.setMarkId(markId);

        } catch (Exception e) {
            log.error("轮询接口，获取图片结果失败，失败markId为:{}", markId, e);
        }
        return taskProcessMessage;
    }

    /**
     * 批量轮询任务结果
     * 支持传统markId和MJ jobId
     *
     * @param markIdList 任务ID列表，可以是markId或jobId
     * @return
     */
    public List<TaskProcessMessage> batchProcessTask(List<String> markIdList) {
        List<TaskProcessMessage> resultList = new ArrayList<>();

        if (CollectionUtils.isEmpty(markIdList)) {
            return resultList;
        }

        for (String taskId : markIdList) {
            try {
                resultList.add(processTask(taskId));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return resultList;
    }

    /**
     * 检查是否为Midjourney任务
     * 通过检查是否存在MJ任务的Redis记录来判断
     */
    private boolean isMidjourneyTask(String taskId) {
        if (StringUtil.isBlank(taskId)) {
            return false;
        }

        return taskId.startsWith(TTAPI_MARKID_PREFIX);
    }

    /**
     * 处理Midjourney任务
     * 重新梳理逻辑，使用专门的dealSuccessOrFailureTaskByJobId方法
     */
    private TaskProcessMessage processMidjourneyTask(String markId) {
        TaskProcessMessage taskProcessMessage = new TaskProcessMessage();

        // 从Redis获取loginName
        String loginName = redisService.stringGet(markId);
        if (StringUtil.isBlank(loginName)) {
            // markId存在但loginName不存在，使用jobId查询数据库
            return dealSuccessOrFailureTaskByJobId(markId, null);
        }

        // 从用户hash中获取任务状态index
        Integer index = (Integer) redisService.getDataFromHash(loginName, markId);

        // 任务的结果已经返回（index为null说明任务已完成）
        if (Objects.isNull(index)) {
            return dealSuccessOrFailureTaskByJobId(markId, loginName);
        }

        // 设置任务基本信息
        taskProcessMessage.setIndex(index);
        taskProcessMessage.setMarkId(markId);

        // 根据index值设置状态
        if (index == -1) {
            taskProcessMessage.setStatus(PromptStatus.newbuilt.getValue());
        } else if (index > 0) {
            // 任务进入排队队列
            taskProcessMessage.setStatus(PromptStatus.pending.getValue());
        } else if (index == 0) {
            try {
                // 检查超时任务
                TaskProcessMessage timeOutTaskMessage = dealMjTimeOutTask(markId, loginName);
                if (!Objects.isNull(timeOutTaskMessage)) {
                    return timeOutTaskMessage;
                }
                taskProcessMessage.setStatus(PromptStatus.running.getValue());
            } catch (Exception e) {
                log.error("轮询接口，MJ图片正在生图失败，失败markId为:{}", markId, e);
            }
        }

        return taskProcessMessage;
    }

    /**
     * 专门处理Midjourney任务的成功或失败情况
     */
    private TaskProcessMessage dealSuccessOrFailureTaskByJobId(String markId, String loginName) {
        TaskProcessMessage taskProcessMessage = new TaskProcessMessage();
        try {
            LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<>();
            qw.eq(PromptRecord::getMarkId, markId);
            if (StringUtil.isNotBlank(loginName)) {
                qw.eq(PromptRecord::getLoginName, loginName);
            }
            PromptRecord originPromptRecord = promptRecordMapper.selectOne(qw);

            if (!Objects.isNull(originPromptRecord)) {
                // 检查是否有失败信息
                if (StringUtil.isNotBlank(originPromptRecord.getFailureMessage())) {
                    taskProcessMessage.setPromptId(originPromptRecord.getPromptId());
                    taskProcessMessage.setMarkId(originPromptRecord.getMarkId());

                    if (PromptStatus.cancel.getValue().equals(originPromptRecord.getFailureMessage())) {
                        taskProcessMessage.setStatus(PromptStatus.cancel.getValue());
                    } else {
                        taskProcessMessage.setStatus(PromptStatus.failure.getValue());
                    }

                    // 清理Redis中的任务标记（如果存在markId）
                    if (StringUtil.isNotBlank(originPromptRecord.getMarkId())) {
                        redisService.delete(originPromptRecord.getMarkId());
                    }

                    return taskProcessMessage;
                }

                // 查询对应的图片信息
                LambdaQueryWrapper<PromptFile> lqwf = new LambdaQueryWrapper<>();
                lqwf.eq(PromptFile::getPromptId, originPromptRecord.getPromptId());
                lqwf.eq(PromptFile::getLoginName, originPromptRecord.getLoginName());
                lqwf.eq(PromptFile::getDel, Boolean.FALSE);
                List<PromptFile> promptFiles = promptFileMapper.selectList(lqwf);

                // 检查图片是否已经生成完成
                if (!CollectionUtils.isEmpty(promptFiles) && originPromptRecord.getBatchSize() == promptFiles.size()) {
                    // 构建图片URL列表
                    List<TaskProcessMessage.ImgUrl> imgUrlList = new ArrayList<>();
                    for (PromptFile promptFile : promptFiles) {
                        TaskProcessMessage.ImgUrl imgUrl = new TaskProcessMessage.ImgUrl();
                        imgUrl.setImgUrl(promptFile.getFileUrl());
                        imgUrl.setThumbnailUrl(promptFile.getThumbnailUrl());
                        imgUrl.setHighThumbnailUrl(promptFile.getHighThumbnailUrl());
                        imgUrl.setMiniThumbnailUrl(promptFile.getMiniThumbnailUrl());
                        imgUrl.setHighMiniUrl(promptFile.getHighMiniUrl());
                        imgUrl.setRealHeight(promptFile.getHeight());
                        imgUrl.setRealWidth(promptFile.getWidth());
                        imgUrl.setSensitive(promptFile.getSensitiveMessage());
                        imgUrl.setImgName(promptFile.getFileName());
                        imgUrlList.add(imgUrl);
                    }

                    // 设置成功状态
                    taskProcessMessage.setStatus(PromptStatus.success.getValue());
                    taskProcessMessage.setPromptId(originPromptRecord.getPromptId());
                    taskProcessMessage.setMarkId(originPromptRecord.getMarkId());
                    taskProcessMessage.setImg_urls(imgUrlList);
                    taskProcessMessage.setPrompt(originPromptRecord.getPrompt());
                    taskProcessMessage.setFeatureName(originPromptRecord.getFeatureName());

                    // 清理Redis中的任务标记（如果存在markId）
                    if (StringUtil.isNotBlank(originPromptRecord.getMarkId())) {
                        redisService.delete(originPromptRecord.getMarkId());
                    }
                } else {
                    // 图片还没有生成完成，检查是否超时
                    TaskProcessMessage timeOutTaskMessage = dealMjTimeOutTask(
                            originPromptRecord.getMarkId(), loginName
                    );
                    if (!Objects.isNull(timeOutTaskMessage)) {
                        return timeOutTaskMessage;
                    }

                    // 如果数据还没有完全入库，则算为执行中
                    taskProcessMessage.setIndex(0);
                    taskProcessMessage.setStatus(PromptStatus.running.getValue());
                    taskProcessMessage.setMarkId(originPromptRecord.getMarkId());
                }
            } else {
                // 没有找到对应的PromptRecord，返回失败状态
                taskProcessMessage.setMarkId(markId);
                taskProcessMessage.setStatus(PromptStatus.failure.getValue());
                taskProcessMessage.setInfo("任务记录不存在");
            }

        } catch (Exception e) {
            log.error("轮询接口，获取MJ图片结果失败，失败markId为:{}", markId, e);
            taskProcessMessage.setStatus(PromptStatus.failure.getValue());
            taskProcessMessage.setInfo("查询任务结果异常");
        }

        return taskProcessMessage;
    }

    /**
     * 处理MJ任务超时情况
     */
    private TaskProcessMessage dealMjTimeOutTask(String markId, String loginName) {
        TaskProcessMessage taskProcessMessage = new TaskProcessMessage();

        // 任务开始执行时间检查
        Long userFinishTaskTimes = System.currentTimeMillis();
        Long userTaskTimes = (Long) redisService.get(USER_TASK_TIMESTAMP + markId);

        Boolean flagTimeOut = Boolean.FALSE;
        if (!Objects.isNull(userTaskTimes) && (userFinishTaskTimes >= userTaskTimes)) {
            // MJ任务超时时间设置为5分钟
            Boolean beMoreTime = ((userFinishTaskTimes - userTaskTimes) > 5 * 60 * 1000) ? Boolean.TRUE : Boolean.FALSE;
            if (beMoreTime) {
                flagTimeOut = Boolean.TRUE;
            }
        }

        if (flagTimeOut) {
            taskProcessMessage.setMarkId(markId);
            taskProcessMessage.setInfo("Task timeout");
            taskProcessMessage.setStatus(PromptStatus.failure.getValue());

            // 删除Redis中的任务标记
            if (StringUtil.isNotBlank(markId)) {
                redisService.delete(markId);
            }

            // 删除时间戳
            try {
                redisService.delete(USER_TASK_TIMESTAMP + markId);
            } catch (Exception e) {
                log.error("dealMjTimeOutTask 删除时间戳失败", e);
            }

            // 这里可以调用MJ服务的失败处理逻辑
//            loadBalanceService.dealFailureTask(markId, jobId, loginName, "MJ Task timeout");
            //查询markId对应的jobId 通过record表
            LambdaQueryWrapper<PromptRecord> qw = new LambdaQueryWrapper<>();
            qw.eq(PromptRecord::getMarkId, markId);
            qw.eq(PromptRecord::getLoginName, loginName);
            PromptRecord promptRecord = promptRecordMapper.selectOne(qw);
            if (Objects.isNull(promptRecord)) {
                return taskProcessMessage;
            }
            String jobId = promptRecord.getPromptId();
            midjourneyService.handleTaskTimeout(jobId, loginName);

            return taskProcessMessage;
        }

        return null;
    }


    public List<UserTaskQueue> getUserTaskQueue(User user) {
        List<UserTaskQueue> userTaskQueueList = new ArrayList<>();
        try {
            Query query = new Query();
            //查询用户拉黑列表
            query.addCriteria(Criteria.where("userId").is(user.getId()));
            query.with(Sort.by(Sort.Direction.ASC, "id"));
            userTaskQueueList = mongoTemplate.find(query, UserTaskQueue.class);
        } catch (Exception e) {
            log.error("用户：{} 获取用户预载任务列表失败", user.getId(), e);
            throw new ServerInternalException("Failed to retrieve the user's following or followers list");
        }
        return userTaskQueueList;
    }

    public String addUserTaskQueue(TaskQueueParams taskQueueParams, User user) {
        RLock lock = redissonClient.getLock(PRELOADING_LOCK_PREFIX + user.getLoginName());
        try {
            lock.lock();

            String taskId = "";

            //校验用户的预载任务额度是否满足
            if (!checkUserTaskQueueAndAdd(user)) {
                throw new LogicException(LogicErrorCode.EXCEED_TASK_QUEUE);
            }

            if (!Objects.isNull(taskQueueParams)) {
                UserTaskQueue userTaskQueue = new UserTaskQueue();
                userTaskQueue.setUserId(user.getId());
                userTaskQueue.setLoginName(user.getLoginName());
                userTaskQueue.setGenInfo(JsonUtils.writeToString(taskQueueParams.getGenInfo()));
                userTaskQueue.setFeaturesTypeValue(taskQueueParams.getFeaturesTypeValue());
                userTaskQueue.setCreateTime(LocalDateTime.now());

                UserTaskQueue insertedUserTaskQueue = userTaskQueueRepository.insert(userTaskQueue);
                //返回任务相关的id
                taskId = insertedUserTaskQueue.getId();
            }

            return taskId;
        } catch (Exception e) {
            log.error("用户：{},新增预载任务：{} 失败", user.getId(), user.getLoginName(), e);
            throw new ServerInternalException("Add TaskQueue Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public Boolean deleteUserTaskQueue(List<String> taskQueueIds, User user) {
        RLock lock = redissonClient.getLock("taskQueue:user:" + user.getLoginName());
        try {
            lock.lock();

            if (!CollectionUtils.isEmpty(taskQueueIds)) {
                Query query = new Query();
                query.addCriteria(Criteria.where("userId").is(user.getId()));
                query.addCriteria(Criteria.where("id").in(taskQueueIds));

                // 执行删除操作并判断是否删除了至少一个文档
                return mongoTemplate.remove(query, UserTaskQueue.class).getDeletedCount() > 0;
            }
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("用户：{},删除预载任务：{} 失败", user.getId(), user.getLoginName(), e);
            throw new ServerInternalException("Delete TaskQueue Failed");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    public UserTaskQueue getUserTask(String preloadId, User user) {
        UserTaskQueue userTaskQueue = new UserTaskQueue();
        try {
            Query query = new Query();
            //查询用户预载列表
            query.addCriteria(Criteria.where("id").is(preloadId));
            query.with(Sort.by(Sort.Direction.DESC, "id"));
            userTaskQueue = mongoTemplate.findOne(query, UserTaskQueue.class);
        } catch (Exception e) {
            log.error("用户：{} 获取预载任务失败", user.getId(), e);
            throw new ServerInternalException("Failed to get user preload task");
        }

        return userTaskQueue;
    }

    public UserTaskQueue getUserTaskAndDelete(String preloadId, User user) {
        UserTaskQueue userTaskQueue = new UserTaskQueue();

        RLock lock = redissonClient.getLock("taskQueue:user:" + user.getLoginName());
        try {
            lock.lock();

            Query query = new Query();
            //查询用户预载列表
            query.addCriteria(Criteria.where("id").is(preloadId));
            query.addCriteria(Criteria.where("userId").is(user.getId()));

            userTaskQueue = mongoTemplate.findOne(query, UserTaskQueue.class);

            if (!Objects.isNull(userTaskQueue)) {

                // 执行删除操作并判断是否删除了至少一个文档
                mongoTemplate.remove(query, UserTaskQueue.class).getDeletedCount();
            }
        } catch (Exception e) {
            log.error("用户：{} 获取预载任务失败", user.getId(), e);
            throw new ServerInternalException("Failed to get user preload task");
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return userTaskQueue;
    }

    public R<Map<String, Object>> executeTask(String preloadId, UserTaskQueue userTaskQueue, User user, HttpServletRequest request) throws IOException {
        R<Map<String, Object>> result = new R<>();

        String featureName = userTaskQueue.getFeaturesTypeValue();
        if (StringUtil.isNotBlank(featureName)) {

            JsonNode jsonNode = JsonUtils.writeStringToJsonNode(userTaskQueue.getGenInfo());

            switch (featureName) {
                case "removeBg":
                    DealImgParams removeBgPara = JsonUtils.fromString(jsonNode.path("dealImgParams").asText(), DealImgParams.class);
                    result = genController.rmBackgroundPicture(removeBgPara, user, request);
                    break;
                case "upscale":
                    DealImgParams upscalePara = JsonUtils.fromString(jsonNode.path("dealImgParams").asText(), DealImgParams.class);
                    result = genController.hiresFixPicture(upscalePara, user, request);
                    break;
                case "inpaint":
                    //兼容旧的接口
                    if (jsonNode.has("redrawImg")) {
                        String genParameters = jsonNode.path("genParameters").asText();
                        MultipartFile multipartFile = FileUtils.convert(jsonNode.path("redrawImg").asText(), user.getLoginName() + System.currentTimeMillis() + ".png", "image/png");
                        result = genController.localRedraw(genParameters, multipartFile, user, request);
                    } else {
                        GenGenericPara inpaintPara = JsonUtils.fromString(jsonNode.path("genParameters").asText(), GenGenericPara.class);
                        result = genController.inpaint(inpaintPara, user, request);
                    }
                    break;
                case "expand":
                    GenGenericPara enlargeImagePara = JsonUtils.fromString(jsonNode.path("genParameters").asText(), GenGenericPara.class);
                    result = genController.enlargeImage(enlargeImagePara, user, request);
                    break;
                case "lineRecolor":
                    GenGenericPara lineRecolorPara = JsonUtils.fromString(jsonNode.path("genParameters").asText(), GenGenericPara.class);
                    result = genController.lineRecolor(lineRecolorPara, user, request);
                    break;
                default:
                    GenGenericPara genGenericPara = JsonUtils.fromString(jsonNode.path("genParameters").asText(), GenGenericPara.class);
                    result = genController.create(genGenericPara, user, request);
                    break;
            }
        }

        if (!Objects.isNull(result) && !Objects.isNull(result.getData()) && StringUtil.isNotBlank(preloadId)) {
            Map<String, Object> resultMap = result.getData();
            if (!Objects.isNull(resultMap)) {
                resultMap.put("preloadId", preloadId);
                result.setData(resultMap);
            }
        } else {
            log.info("用户：{} 执行预载任务失败,结果集result:{},预载preloadId:{}", user.getLoginName(), JsonUtils.writeToString(request), preloadId);
            result = R.fail(400, "Task Execute Failed !");
        }

        return result;

    }

    public Boolean checkUserTaskQueueAndAdd(User user) {
        try {
            List<String> userNotFinishTaskList = redisService.getAllKeysFromHash(user.getLoginName());
            List<UserTaskQueue> userTaskQueueList = getUserTaskQueue(user);

            //如果用户的生图任务列表为空，则直接返回true
            if (CollectionUtils.isEmpty(userTaskQueueList)) {
                return Boolean.TRUE;
            } else {
                VipStandards vipStandards = genService.getVipStandards(user);

                //如果已经存在的生图任务数大或于等于会员批量生图数标准，则直接返回false
                if (vipStandards.getTaskQueue() > (userNotFinishTaskList.size() + userTaskQueueList.size())) {
                    return Boolean.TRUE;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Boolean.FALSE;
        }
        return Boolean.FALSE;
    }
}
