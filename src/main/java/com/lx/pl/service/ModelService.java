package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lx.pl.db.mysql.gen.entity.GptModelAbout;
import com.lx.pl.db.mysql.gen.mapper.GptModelAboutMapper;
import com.lx.pl.dto.ModelInformation;
import com.lx.pl.enums.ClientType;
import com.lx.pl.util.JsonUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/13
 * @description 模型相关Service
 */
@Slf4j
@Service
public class ModelService {
    @Resource
    RedisService redisService;
    @Resource
    private GptModelAboutMapper gptModelAboutMapper;

    /**
     * 获取模型列表
     */
    public List<ModelInformation.ModelAbout> listModels(String platform) throws IOException {
        List<ModelInformation.ModelAbout> modelAboutList = new ArrayList<>();

        //定义redis中不同数据的key
        String webModelListKey = "webModelListKey";
        String iosModelListKey = "iosModelListKey";
        String androidModelListKey = "androidModelListKey";
        String modelListResult = "";

        //根据不同的客户端类型，返回不同的模型列表
        if (StringUtil.isBlank(platform)) {
            return modelAboutList;
        }
        String clientType = platform.toLowerCase();
        if (StringUtil.isNotBlank(clientType)) {
            switch (clientType) {
                case "ios":
                    modelListResult = (String) redisService.get(iosModelListKey);
                    break;
                case "android":
                    modelListResult = (String) redisService.get(androidModelListKey);
                    break;
                case "web":
                    modelListResult = (String) redisService.get(webModelListKey);
                    break;
            }
        }

        if (StringUtil.isNotBlank(modelListResult)) {
            modelAboutList.addAll(JsonUtils.writeToList(modelListResult, ModelInformation.ModelAbout.class));
        } else {
            List<ModelInformation.ModelAbout> modelAbouts = buildModelList(clientType);
            if (StringUtil.isNotBlank(clientType)) {
                switch (clientType) {
                    case "ios":
                        redisService.set(iosModelListKey, JsonUtils.writeToString(modelAbouts));
                        break;
                    case "android":
                        redisService.set(androidModelListKey, JsonUtils.writeToString(modelAbouts));
                        break;
                    case "web":
                        redisService.set(webModelListKey, JsonUtils.writeToString(modelAbouts));
                        break;
                }
            }
            modelAboutList = modelAbouts;
        }
        return modelAboutList;

    }

    public List<ModelInformation.ModelAbout> buildModelList(String platform) {
        List<GptModelAbout> modelAboutList = gptModelAboutMapper.selectList(new LambdaQueryWrapper<GptModelAbout>().orderByAsc(GptModelAbout::getModelOrder));

        List<ModelInformation.ModelAbout> modelAboutDtos = new ArrayList<>();

        if (StringUtil.isNotBlank( platform)) {
            modelAboutList = modelAboutList.stream()
                    .filter(modelAbout -> {
                        String plat = modelAbout.getPlatform();
                        return plat != null && Arrays.asList(plat.split(",")).contains(platform);
                    })
                    .collect(Collectors.toList());
        }

        for (GptModelAbout gptModelAbout : modelAboutList) {
            ModelInformation.ModelAbout modelAboutDto = new ModelInformation.ModelAbout();
            BeanUtils.copyProperties(gptModelAbout, modelAboutDto);
            if (com.lx.pl.util.StringUtils.isNotBlank(gptModelAbout.getDefaultConfig())) {
                try {
                    modelAboutDto.setDefaultConfig(JsonUtils.fromString(gptModelAbout.getDefaultConfig(), ModelInformation.DefaultConfig.class));
                } catch (Exception e) {
                    log.error("模型列表转换失败", e);
                    modelAboutDto.setDefaultConfig(ModelInformation.DefaultConfig.builder().width(1024).height(1024).steps(0).seed(-1l).build());
                }
            } else {
                modelAboutDto.setDefaultConfig(ModelInformation.DefaultConfig.builder().width(1024).height(1024).steps(0).seed(-1l).build());
            }
            if (com.lx.pl.util.StringUtils.isNotBlank(gptModelAbout.getSupportStyleList())) {
                try {
                    modelAboutDto.setSupportStyleList(JsonUtils.writeToList(gptModelAbout.getSupportStyleList(), ModelInformation.SupportStyle.class));
                } catch (Exception e) {
                    log.error("模型列表转换失败", e);
                    modelAboutDto.setSupportStyleList(new ArrayList<>());
                }
            } else {
                modelAboutDto.setSupportStyleList(new ArrayList<>());
            }
            modelAboutDtos.add(modelAboutDto);
        }
        return modelAboutDtos;
    }

    @SneakyThrows
    public ModelInformation.ModelAbout getByModelId(String modelId) {
        if (StringUtils.isBlank(modelId)) {
            return null;
        }

        ModelInformation.ModelAbout model = null;
        List<ModelInformation.ModelAbout> modelList = listModels(ClientType.web.getValue());
        if (CollectionUtils.isNotEmpty(modelList)) {
            model = modelList.stream().filter(modelAbout -> Objects.equals(modelAbout.getModelId(), modelId)).findFirst().orElse(null);
        }

        if (model == null) {
            modelList = listModels(ClientType.ios.getValue());
            if (CollectionUtils.isNotEmpty(modelList)) {
                model = modelList.stream().filter(modelAbout -> Objects.equals(modelAbout.getModelId(), modelId)).findFirst().orElse(null);
            }
        }

        if (model == null) {
            modelList = listModels(ClientType.android.getValue());
            if (CollectionUtils.isNotEmpty(modelList)) {
                model = modelList.stream().filter(modelAbout -> Objects.equals(modelAbout.getModelId(), modelId)).findFirst().orElse(null);
            }
        }

        return model;
    }
}
